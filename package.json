{"name": "admin.net", "type": "module", "version": "2.4.33", "lastBuildTime": "2024.11.29", "description": "Admin.NET 站在巨人肩膀上的 .NET 通用权限开发框架", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "vite", "build": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite build", "lint-fix": "eslint --fix src/", "format": "prettier --write .", "build-api": "cd api_build/ && build.bat", "build-approvalFlow-api": "cd api_build/ && build.bat approvalFlow", "build-dingTalk-api": "cd api_build/ && build.bat dingTalk", "build-goView-api": "cd api_build/ && build.bat goView"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@logicflow/core": "^2.0.9", "@logicflow/extension": "^2.0.13", "@microsoft/signalr": "^8.0.7", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.9", "@vueuse/core": "^12.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "async-validator": "^4.2.5", "axios": "^1.7.8", "bwip-js": "^4.6.0", "countup.js": "^2.8.0", "cropperjs": "^1.6.2", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.8.8", "ezuikit-js": "^8.1.1-alpha.3", "js-cookie": "^3.0.5", "js-table2excel": "^1.1.2", "json-editor-vue": "^0.17.3", "jsplumb": "^2.15.6", "lodash-es": "^4.17.21", "md-editor-v3": "^4.21.2", "mitt": "^3.0.1", "monaco-editor": "^0.52.0", "mqtt": "^4.3.8", "nprogress": "^0.2.0", "pinia": "^2.2.8", "print-js": "^1.6.0", "push.js": "^1.0.12", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.13.1", "relation-graph": "^2.2.10", "screenfull": "^6.0.2", "sm-crypto-v2": "^1.9.3", "sortablejs": "^1.15.3", "splitpanes": "^3.1.5", "vcrontab-3": "^3.3.22", "vform3-builds": "^3.0.10", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.6", "vue-draggable-plus": "^0.5.6", "vue-grid-layout": "3.0.0-beta1", "vue-i18n": "^10.0.5", "vue-json-pretty": "^2.4.0", "vue-plugin-hiprint": "0.0.60", "vue-router": "^4.5.0", "vue-signature-pad": "^3.0.2", "vue3-tree-org": "^4.2.2", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@plugin-web-update-notification/vite": "^1.7.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.1", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.16.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "^3.5.13", "code-inspector-plugin": "^0.18.2", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "less": "^4.2.1", "prettier": "^3.4.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.81.0", "terser": "^5.36.0", "typescript": "^5.7.2", "vite": "^5.4.9", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression2": "^1.3.3", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.4.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["admin.net", "vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}