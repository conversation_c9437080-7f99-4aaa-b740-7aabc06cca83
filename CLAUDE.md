# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

基于 Admin.NET 的通用权限开发框架前端实现，采用 Vue 3 + TypeScript + Element Plus 技术栈，使用 Vite 构建。项目采用 Vue 3 Composition API setup 语法糖进行开发。

## 开发命令

### 基础命令
```bash
# 安装依赖
pnpm install

# 开发服务器
pnpm run dev

# 生产构建
pnpm run build
```

### 代码质量
```bash
# ESLint 检查和修复
pnpm run lint-fix

# 代码格式化
pnpm run format
```

### API 生成
```bash
# 生成主 API 服务
pnpm run build-api

# 生成专用 API 服务
pnpm run build-approvalFlow-api
pnpm run build-dingTalk-api
pnpm run build-goView-api
```

## 项目架构

### 核心技术栈
- **主框架**: Vue 3.5+ with Composition API
- **UI 组件库**: Element Plus 2.8+
- **状态管理**: Pinia 2.2+
- **路由管理**: Vue Router 4.5+
- **构建工具**: Vite 5.4+
- **类型系统**: TypeScript 5.7+
- **HTTP 客户端**: Axios 1.7+

### 关键架构特性

**API 服务架构 (`src/api-services/` 和 `src/api-plugins/`)**
- `api-services/`: 主要业务 API，通过 OpenAPI 规范生成
- `api-plugins/`: 专用插件 API (审批流、钉钉、GoView 等)
- API 代码通过 `api_build/` 批处理脚本从 Swagger 文档自动生成

**路由系统 (`src/router/`)**
- 支持前端控制路由和后端控制路由两种模式
- `frontEnd.ts`: 前端控制路由逻辑
- `backEnd.ts`: 后端控制路由逻辑
- 路由模式通过 `themeConfig.isRequestRoutes` 配置控制

**状态管理 (`src/stores/`)**
- 使用 Pinia 进行状态管理
- `userInfo.ts`: 用户信息管理
- `routesList.ts`: 路由列表状态
- `themeConfig.ts`: 主题配置管理
- `tagsViewRoutes.ts`: 标签页路由管理

**权限认证系统**
- JWT token 认证机制，支持 token 自动刷新
- 权限控制基于路由 meta 信息和用户角色
- 支持动态路由加载

**UI 组件架构**
- 基于 Element Plus 组件库
- 自定义组件统一存放在 `src/components/`
- 支持多主题和多布局模式

### 开发规范

**API 开发规范**
- 优先使用 `api-services/` 和 `api-plugins/` 中的生成代码
- API 更新后需要重新运行生成命令同步接口

**路由开发规范**
- 新增路由需要在后端控制模式下通过后端接口配置路由数据
- 动态路由需要在 `meta.isDynamic` 设置为 true

**状态管理规范**
- 统一使用 Pinia 进行状态管理
- 充分利用 Vue 3 的 reactive/ref 响应式系统

**样式开发规范**
- 使用 SCSS 作为 CSS 预处理器
- 主题相关样式统一存放在 `src/theme/` 目录
- 响应式布局相关样式存放在 `src/theme/media/` 目录

**国际化规范**
- 页面级别翻译文件存放在 `src/i18n/lang/`
- 新增页面的翻译统一存放在 `src/i18n/pages/`

## 项目配置

### 环境配置
- 开发环境配置在 `.env` 文件中
- 生产环境可通过 `public/config.js` 覆盖配置到 `window.__env__`

### 构建配置
- 支持 CDN 加速配置 (`VITE_OPEN_CDN`)
- 生产环境会移除 console 和 debugger
- 支持代码压缩和优化

### 开发工具
- 集成 Code Inspector 插件，按 Shift 键可快速定位组件代码
- 支持热重载和快速刷新
- 内置代码检查和格式化工具


### 开始事项
- 所有api均在src/api-services目录下
- 优先参考代码库里已有的组件和方式进行开发，当判断现有组件无法实现时，使用context 7 查看element-plus选择合适的组件
api调用方式
``` code
const res = await getAPI(UniversityApi).apiUniversityParkListGet();
```