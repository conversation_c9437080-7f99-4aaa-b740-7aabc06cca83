<template>
	<div class="data-statistics-container">
		<div class="data-statistics-header">
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
				<el-tab-pane :label="item.label" :name="item.name" v-for="item in tabs" :key="item.name">

				</el-tab-pane>
			</el-tabs>
		</div>
		<div class="data-statistics-content" v-if="activeName === 1">
			<el-statistic title="毕业生人数:" :value="numGraduatesTransition" />
			<div class="data-statistics-content-chart">
				<chartPerson />
			</div>
			<div class="data-statistics-content-chart">
				<chartBar :data="data" />
			</div>
			<div class="data-statistics-content-chart">
				<chartBar :data="data1" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import chartPerson from './components/chartPerson.vue';
import { useTransition } from '@vueuse/core'
import chartBar from './components/chartBar.vue';
const numGraduates = ref(0)
const numGraduatesTransition = useTransition(numGraduates, {
	duration: 1500,
})
numGraduates.value = 1123

const tabs = ref([
	{
		label: '求职者分析',
		name: 1,
	},
	{
		label: '企业分析',
		name: 2,
	},
	{
		label: '职位分析',
		name: 3,
	},
]);
const activeName = ref(1);
const handleClick = (tab: any, event: any) => {
	console.log(tab, event);
};

const data = ref()
const data1 = ref()

onMounted(() => {
	setTimeout(() => {
	data.value = [
	{ value: 10, name: '北京市区西城区朝阳街1号' },
	{ value: 52, name: '上海市区黄浦区南京东路2号' },
	{ value: 200, name: '广州市白云区白云街3号' },
	{ value: 334, name: '深圳市南山区南山街4号' },
	{ value: 390, name: '杭州市西湖区西湖街5号' },
	{ value: 330, name: '成都市武侯区武侯街6号' },
	{ value: 220, name: '重庆市渝中区渝中街7号' },
	{ value: 10, name: '北京郊区朝阳区朝阳街1号' },
	{ value: 52, name: '上海郊区浦东新区浦东街2号' },
	{ value: 200, name: '广州市白云区白云街3号' },
	{ value: 334, name: '深圳市南山区南山街4号' },
	{ value: 390, name: '杭州市西湖区西湖街5号' },
	{ value: 330, name: '成都市武侯区武侯街6号' },
	{ value: 220, name: '重庆市渝中区渝中街7号' },
	{ value: 10, name: '北京其他朝阳街1号' },
	{ value: 52, name: '上海其他南京东路2号' },
	{ value: 200, name: '广州其他' },
	{ value: 334, name: '深圳其他' },
	{ value: 390, name: '杭州其他' },
	{ value: 330, name: '成都其他' },
	{ value: 220, name: '重庆其他' },
]

data1.value = [
{ value: 200, name: '广州其他' },
	{ value: 334, name: '深圳其他' },
	{ value: 390, name: '杭州其他' },
	{ value: 330, name: '成都其他' },
	{ value: 220, name: '重庆其他' },
]
}, 3000);
})
</script>

<style scoped lang="scss">
.data-statistics-container {
	width: 100%;
	height: 100%;
	padding: 10px 20px;
	background: #fff;
	box-sizing: border-box;

	.data-statistics-header {}

	.data-statistics-content {
		.el-statistic {
			display: flex;
			align-items: center;
			gap: 10px;

			:deep(.el-statistic__head) {
				margin: 0;
			}

			:deep(.el-statistic__content) {
				color: red;
			}
		}

		.data-statistics-content-chart {
			margin-top: 10px;
		}
	}
}
</style>