<template>
    <div class="chart-bar-container">
        <div class="chart-content-bar" ref="chartRef"></div>
    </div>
</template>

<script setup lang="ts">
import { getChartBarOption } from './chartBarOption';
import * as echarts from 'echarts';
import { ref, onMounted, reactive, watch, onUnmounted, computed } from 'vue';
const props = withDefaults(defineProps<{
    data: { value: number; name: string }[];
}>(), {
    data: () => []
});
const chartRef = ref<HTMLDivElement>();

const chart = ref<echarts.ECharts>();
onMounted(() => {
    const chartMain = echarts.init(chartRef.value);

    const option = getChartBarOption('期望工作地', props.data);

    console.log(option);
    console.log(chart.value);

    chartMain.setOption(option);
    chart.value = chartMain;
    window.addEventListener('resize', () => {
        chart.value?.resize();
    });
});

onUnmounted(() => {
    chart.value?.dispose();
});
const data = computed(() => props.data);

watch(()=>data.value, (newVal,oldVal) => {
    const option = getChartBarOption('期望工作地', newVal);
    chart.value?.setOption(option);
}, {
    deep: true,
    immediate: true
});

</script>
<style scoped lang="scss">
.chart-bar-container {
    width: 100%;
    height: 700px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .chart-content-bar {
        width: 100%;
        height: 100%;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
        }
    }
}
</style>