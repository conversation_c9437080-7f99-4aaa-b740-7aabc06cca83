// 计算标签的最大长度和自适应旋转角度
const calculateLabelRotation = (labels: string[]) => {
	const maxLength = Math.max(...labels.map(label => label.length));
	const labelCount = labels.length;

	// 根据标签长度和数量计算旋转角度
	if (maxLength <= 4 && labelCount <= 6) {
		return 0; // 短标签且数量少，不旋转
	} else if (maxLength <= 6 && labelCount <= 8) {
		return 15; // 中等长度，轻微倾斜
	} else if (maxLength <= 10 && labelCount <= 12) {
		return 30; // 较长标签，中度倾斜
	} else if (maxLength <= 15) {
		return 45; // 长标签，较大倾斜
	} else {
		return 60; // 很长标签，大角度倾斜
	}
};

// 根据旋转角度调整底部间距
const calculateBottomMargin = (rotation: number) => {
	if (rotation === 0) return '8%';
	if (rotation <= 15) return '12%';
	if (rotation <= 30) return '15%';
	if (rotation <= 45) return '18%';
	return '22%';
};

export const getChartBarOption = (title: string, data:{value:number,name:string}[] = []) => {
	const labels = data.map(item => item.name);
	const rotation = calculateLabelRotation(labels);
	const bottomMargin = calculateBottomMargin(rotation);

	return {
		title: {
			text: title,
			left: 40,
			top: 10,
			textStyle: {
			  fontSize: 18,
			  fontWeight: 'bold',
			  color: '#333'
			}
		  },
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow',
			},
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: bottomMargin,
			containLabel: true,
		},
		xAxis: [
			{
				type: 'category',
				data: labels,
				axisTick: {
					alignWithLabel: true,
				},
				axisLabel: {
					interval: 0,
					rotate: rotation,
					fontSize: 12,
					color: '#666',
					// 当旋转角度较大时，调整标签位置
					margin: rotation > 30 ? 15 : 8,
					// 长文本时启用文本溢出处理
					overflow: 'truncate',
					width: rotation > 45 ? 80 : undefined,
					formatter: function(value: string) {
						// 当标签很长时，进行智能截断
						if (value.length > 12 && rotation < 45) {
							return value.length > 15 ? value.substring(0, 12) + '...' : value;
						}
						return value;
					}
				}
			},
		],
		yAxis: [
			{
				type: 'value',
			},
		],
		series: [
			{
				name: 'Direct',
				type: 'bar',
				barWidth: '60%',
				data: data.map(item => item.value),
				label: {
					show: true,
					position: 'top',
					formatter: '{c}',
					fontSize: 12,
				},
			},
		],
	};
};
