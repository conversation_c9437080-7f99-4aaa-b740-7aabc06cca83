// 极简男女SVG图标 - 符合现实比例，使用矩形绘制
const symbols = [
    // 男生图标：使用提供的SVG图标
    'path://M512 88m-88 0a88 88 0 1 0 176 0 88 88 0 1 0-176 0Z M744.9 619.6c-21.3 5.7-43.3-6.9-49-28.3l-64.1-239c-1.2-4.5-7.9-3.6-7.9 1V1008c0 8.8-7.2 16-16 16h-64c-8.8 0-16-7.2-16-16V640c0-8.8-7.2-16-16-16s-16 7.2-16 16v368c0 8.8-7.2 16-16 16h-64c-8.8 0-16-7.2-16-16V353.3c0-4.7-6.7-5.6-7.9-1l-64.1 239c-5.7 21.3-27.7 34-49 28.3-21.3-5.7-34-27.6-28.3-49l69.5-260C339 240.7 402.4 192 474.8 192h74.3c72.5 0 135.9 48.7 154.6 118.7l69.5 260c5.7 21.3-6.9 43.2-28.3 48.9z',
    // 女生图标：使用提供的SVG图标
    'path://M512 88m-88 0a88 88 0 1 0 176 0 88 88 0 1 0-176 0Z M744.9 619.6c-21.3 5.7-43.3-7-49-28.3l-64.1-239c-1.2-4.5-7.9-3.6-7.9 1v132c0 19.4 2.9 38.6 8.7 57.1l48.8 156.8c3.2 10.3-4.5 20.8-15.3 20.8H628c-2.2 0-4 1.8-4 4v284c0 8.8-7.2 16-16 16h-64c-8.8 0-16-7.2-16-16V724c0-2.2-1.8-4-4-4h-24c-2.2 0-4 1.8-4 4v284c0 8.8-7.2 16-16 16h-64c-8.8 0-16-7.2-16-16V724c0-2.2-1.8-4-4-4h-38.3c-10.8 0-18.5-10.5-15.3-20.8l48.8-156.8c5.8-18.5 8.7-37.7 8.7-57.1v-132c0-4.7-6.7-5.5-7.9-1l-64.1 239c-5.7 21.3-27.7 34-49 28.3-17.9-4.8-29.7-21-29.7-38.6 0-3.4 0.4-6.9 1.4-10.4l69.5-260C339 240.7 402.4 192 474.8 192h74.3c72.4 0 135.9 48.7 154.6 118.7l69.5 260c5.7 21.3-6.9 43.2-28.3 48.9z'
  ];
  const bodyMax = 100; // 改为100，表示百分比

export const getOption = (manData: number = 0, womanData: number = 0) => {
    return  {
        title: {
          text: '毕业生性别占比统计',
          left: 'center',
          top: 10,
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        // tooltip: {
        //   trigger: 'item',
        //   formatter: function(params: any) {
        //     return `${params.seriesName}: ${params.value}%`;
        //   },
        //   backgroundColor: 'rgba(0, 0, 0, 0.8)',
        //   textStyle: {
        //     color: '#fff'
        //   }
        // },
        legend: {
          data: ['男生', '女生'],
          top: 40,
          textStyle: {
            fontSize: 14,
            color: '#333'
          },
          itemGap: 30
        },
        xAxis: {
          data: ['男生', '女生'],
          axisTick: { show: false },
          axisLine: { show: false },
          axisLabel: {
            show: true,
            fontSize: 16,
            color: '#666',
            margin: 20
          }
        },
        yAxis: {
          max: bodyMax,
          offset: 20,
          splitLine: { show: false },
          axisLabel: { show: false },
          axisTick: { show: false },
          axisLine: { show: false }
        },
        grid: {
          top: 120,
          bottom: 80,
          left: 50,
          right: 50,
          height: 280
        },
        animationDuration: 2000,
        series: [
          // 男生数据
          {
            name: '男生',
            type: 'pictorialBar',
            symbolClip: true,
            symbolBoundingData: bodyMax,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#5DADE2'
                }, {
                  offset: 1, color: '#3498DB'
                }]
              }
            },
            data: [
              {
                value: manData, // 男生占比65%
                symbol: symbols[0],
                label: {
                  show: true,
                  position: 'top',
                  offset: [0, -20],
                  formatter: `${manData}%`,
                  fontSize: 18,
                  fontFamily: 'Arial',
                  color: '#3498DB'
                }
              },
              {
                value: 0,
                symbol: symbols[1],
                label: {
                  show: false
                }
              }
            ],
            z: 10
          },
          // 女生数据
          {
            name: '女生',
            type: 'pictorialBar',
            symbolClip: true,
            symbolBoundingData: bodyMax,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#F1948A'
                }, {
                  offset: 1, color: '#E74C3C'
                }]
              }
            },
            data: [
              {
                value: 0,
                symbol: symbols[0],
                label: {
                  show: false
                }
              },
              {
                value: womanData, // 女生占比35%
                symbol: symbols[1],
                label: {
                  show: true,
                  position: 'top',
                  offset: [0, -20],
                  formatter: `${womanData}%`,
                  fontSize: 18,
                  fontFamily: 'Arial',
                  color: '#E74C3C'
                }
              }
            ],
            z: 10
          },
          // 背景图形
          {
            name: 'background',
            type: 'pictorialBar',
            symbolBoundingData: bodyMax,
            animationDuration: 0,
            itemStyle: {
              color: '#E8E8E8'
            },
            data: [
              {
                value: bodyMax,
                symbol: symbols[0]
              },
              {
                value: bodyMax,
                symbol: symbols[1]
              }
            ],
            z: 1
          }
        ]
      };
}