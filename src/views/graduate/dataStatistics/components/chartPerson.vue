<template>
    <div class="chart-person-container">
        <div class="chart-content" ref="chartRef"></div>
    </div>
</template>

<script setup lang="ts">
import { getOption } from './chartPersonOption';
import * as echarts from 'echarts';
import { ref, onMounted, reactive } from 'vue';

const chartRef = ref<HTMLDivElement>();

// 模拟数据
const genderData = reactive({
  male: 65,    // 男生占比65%
  female: 35   // 女生占比35%
});

onMounted(() => {
    const chart = echarts.init(chartRef.value);
    const option = getOption();
  

    chart.setOption(option);

    // 响应式调整图表大小
    window.addEventListener('resize', () => {
        chart.resize();
    });

    setTimeout(() => {
        const option = getOption(genderData.male, genderData.female);
        chart.setOption(option);
    }, 1000);
});


</script>

<style scoped lang="scss">
.chart-person-container {
    width: 100%;
    max-width: 800px;
    height: 500px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .chart-content {
        width: 100%;
        height: 100%;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
        }
    }
}
</style>