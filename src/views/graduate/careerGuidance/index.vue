<template>
	<div class="career-guidance-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-tabs v-model="state.activeTab" @tab-change="handleTabChange">
				<el-tab-pane label="就业指导" name="1">
					<el-form :model="state.queryParams" ref="queryForm" :inline="true">
						<el-form-item label="标题">
							<el-input v-model="state.queryParams.title" placeholder="标题" clearable />
						</el-form-item>
						
						<el-form-item label="发布状态">
							<el-select v-model="state.queryParams.isPublished" placeholder="发布状态" clearable>
								<el-option label="已发布" :value="true" />
								<el-option label="未发布" :value="false" />
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button-group>
								<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
							</el-button-group>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" icon="ele-Plus" @click="openAddDialog"> 新增 </el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>
				<el-tab-pane label="求职指导" name="2">
					<el-form :model="state.queryParams" ref="queryForm" :inline="true">
						<el-form-item label="标题">
							<el-input v-model="state.queryParams.title" placeholder="标题" clearable />
						</el-form-item>
						<el-form-item label="讲师">
							<el-input v-model="state.queryParams.lecturer" placeholder="讲师" clearable />
						</el-form-item>
						<el-form-item label="发布状态">
							<el-select v-model="state.queryParams.isPublished" placeholder="发布状态" clearable>
								<el-option label="已发布" :value="true" />
								<el-option label="未发布" :value="false" />
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button-group>
								<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
							</el-button-group>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" icon="ele-Plus" @click="openAddDialog"> 新增 </el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>
				<el-tab-pane label="职场能力" name="3">
					<el-form :model="state.queryParams" ref="queryForm" :inline="true">
						<el-form-item label="标题">
							<el-input v-model="state.queryParams.title" placeholder="标题" clearable />
						</el-form-item>
						<el-form-item label="讲师">
							<el-input v-model="state.queryParams.lecturer" placeholder="讲师" clearable />
						</el-form-item>
						<el-form-item label="发布状态">
							<el-select v-model="state.queryParams.isPublished" placeholder="发布状态" clearable>
								<el-option label="已发布" :value="true" />
								<el-option label="未发布" :value="false" />
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button-group>
								<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
							</el-button-group>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" icon="ele-Plus" @click="openAddDialog"> 新增 </el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>
			</el-tabs>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.careerGuidanceData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="title" label="标题" min-width="200" align="left" show-overflow-tooltip />
				<el-table-column prop="classHours" label="课时" width="100" align="center" show-overflow-tooltip />
				<el-table-column label="发布状态" width="100" align="center">
					<template #default="scope">
						<el-tag :type="scope.row.isPublished ? 'success' : 'info'">
							{{ scope.row.isPublished ? '已发布' : '未发布' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="发布时间" width="180" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.publishTime) }}
					</template>
				</el-table-column>
				<el-table-column prop="createUserName" label="发布者" width="120" align="center" show-overflow-tooltip />
				<el-table-column label="创建时间" width="180" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.createTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditDialog(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="deleteItem(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				size="small"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>

		<EditDialog
			v-model:visible="state.dialogVisible"
			:edit-data="state.editData"
			:title="state.editTitle"
			:type="parseInt(state.activeTab)"
			@success="handleQuery"
		/>
	</div>
</template>

<script lang="ts" setup name="careerGuidance">
import { onMounted, reactive } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditDialog from './components/editDialog.vue';
import { getAPI } from '/@/utils/axios-utils';
import { CareerGuidanceApi } from '/@/api-services/api';
import { CareerGuidanceOutput } from '/@/api-services/models/career-guidance-output';
import { formatDate } from '/@/utils/formatTime';

const state = reactive({
	loading: false,
	activeTab: '1',
	careerGuidanceData: [] as Array<CareerGuidanceOutput>,
	queryParams: {
		title: undefined,
		lecturer: undefined,
		isPublished: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0 as any,
	},
	editTitle: '',
	dialogVisible: false,
	editData: null as CareerGuidanceOutput | null,
});

onMounted(async () => {
	await handleQuery();
});

// 切换tab
const handleTabChange = async (tabName: string | number) => {
	state.activeTab = tabName as string;
	resetQuery();
};

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign({}, state.queryParams, state.tableParams);
		let res = await getAPI(CareerGuidanceApi).apiCareerGuidancePageGet(
			parseInt(state.activeTab), // type 参数
			params.title,
			params.lecturer,
			params.isPublished,
			params.page,
			params.pageSize
		);
		state.careerGuidanceData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
		ElMessage.error('查询失败');
	} finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.title = undefined;
	state.queryParams.lecturer = undefined;
	state.queryParams.isPublished = undefined;
	state.tableParams.page = 1;
	await handleQuery();
};

// 打开新增对话框
const openAddDialog = () => {
	const typeMap = { '1': '就业指导', '2': '求职指导', '3': '职场能力' };
	state.editTitle = `添加${typeMap[state.activeTab as keyof typeof typeMap]}`;
	state.editData = null;
	state.dialogVisible = true;
};

// 打开编辑对话框
const openEditDialog = async (row: CareerGuidanceOutput) => {
	const typeMap = { '1': '就业指导', '2': '求职指导', '3': '职场能力' };
	state.editTitle = `编辑${typeMap[state.activeTab as keyof typeof typeMap]}`;
	try {
		const res = await getAPI(CareerGuidanceApi).apiCareerGuidanceDetailGet(row.id);
		state.editData = res.data.result || null;
		state.dialogVisible = true;
	} catch (error) {
		ElMessage.error('获取详情失败');
	}
};

// 删除
const deleteItem = (row: CareerGuidanceOutput) => {
	ElMessageBox.confirm(`确定删除：【${row.title}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(CareerGuidanceApi).apiCareerGuidanceDeletePost(row.id);
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = async (val: number) => {
	state.tableParams.pageSize = val;
	await handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 格式化日期
const formatDateTime = (date: any) => {
	if (!date) return '-';
	return formatDate(new Date(date), 'YYYY-mm-dd HH:MM:SS');
};
</script>

<style lang="scss" scoped>
.career-guidance-container {
	padding: 20px;
}
</style>