<template>
	<el-dialog v-model="dialogVisible" :title="title" width="900px" :before-close="handleClose" class="edit-dialog">
		<el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="dialog-form">
			<el-form-item label="标题" prop="title">
				<el-input v-model="formData.title" placeholder="请输入标题" clearable maxlength="100" show-word-limit />
			</el-form-item>
			<el-form-item label="课时" prop="classHours">
				<el-input v-model="formData.classHours" placeholder="请输入课时" clearable />
			</el-form-item>
			<el-form-item label="讲师" prop="lecturer">
				<el-input v-model="formData.lecturer" placeholder="请输入讲师姓名" clearable />
			</el-form-item>
			<el-form-item label="封面图片" prop="image">
				<div class="upload-container">
					<div v-if="imageUrl" class="image-preview">
						<el-image ref="imageRef" :src="imageUrl" alt="预览图片" :fit="'fill'"
							:preview-src-list="[imageUrl]" />
						<div class="image-overlay">
							<el-icon class="upload-icon" size="20" @click.stop="handleClick">
								<View />
							</el-icon>
							<el-icon class="upload-icon" size="20" @click.stop="handleReupload">
								<Upload />
							</el-icon>
							<el-icon class="delete-icon" size="20" @click.stop="handleDeleteImage">
								<Delete />
							</el-icon>
						</div>
					</div>
					<el-upload ref="uploadRef" v-else :file-list="fileList" :auto-upload="false" :show-file-list="false"
						:on-change="handleFileChange" :before-upload="beforeUpload" accept="image/*"
						class="image-upload">
						<div class="upload-placeholder">
							<el-icon class="upload-icon">
								<Plus />
							</el-icon>
							<div class="upload-text">点击上传图片</div>
							<div class="upload-tip">支持 jpg、png、gif 格式，大小不超过 2MB</div>
						</div>
					</el-upload>
				</div>
			</el-form-item>
			<el-form-item label="视频列表">
				<div class="video-list">
					<div v-for="(video, index) in formData.videos" :key="index" class="video-item">
						<el-row :gutter="10">
							<el-col :span="8">
								<el-input v-model="video.videoTitle" placeholder="视频标题" />
							</el-col>
							<el-col :span="12">
								<el-input v-model="video.videoLink" placeholder="视频链接" />
							</el-col>
							<el-col :span="3">
								<el-input-number v-model="video.sort" placeholder="排序" :min="1" style="width: 100%" />
							</el-col>
							<el-col :span="1">
								<el-button type="danger" icon="ele-Delete" @click="removeVideo(index)" />
							</el-col>
						</el-row>
					</div>
					<el-button type="primary" icon="ele-Plus" @click="addVideo" style="margin-top: 10px">添加视频</el-button>
				</div>
			</el-form-item>
			<el-form-item label="课程简介" prop="shortCourseIntroduction">
				<el-input
					v-model="formData.shortCourseIntroduction"
					type="textarea"
					placeholder="请输入课程简介"
					:rows="3"
					maxlength="500"
					show-word-limit
				/>
			</el-form-item>

			<el-form-item label="课程介绍" prop="courseIntroduction">
				<Editor v-model:get-html="formData.courseIntroduction" height="300px" />
			</el-form-item>

			

			<el-form-item label="发布设置">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-checkbox v-model="formData.isPublished">发布线上</el-checkbox>
					</el-col>
					<el-col :span="12" v-if="formData.isPublished">
						<el-date-picker
							v-model="formData.publishTime"
							type="datetime"
							placeholder="请选择发布时间"
							format="YYYY-MM-DD HH:mm:ss"
							style="width: 100%"
						/>
					</el-col>
				</el-row>
			</el-form-item>
		</el-form>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :loading="submitLoading">
					{{ isEdit ? '更新' : '保存' }}
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, type UploadFile } from 'element-plus';
import { Plus, Upload, Delete, View } from '@element-plus/icons-vue';
import { CareerGuidanceApi } from '/@/api-services/api';
import { getAPI } from '/@/utils/axios-utils';
import { CareerGuidanceOutput } from '/@/api-services/models/career-guidance-output';
import { CareerGuidanceVideoEditingInput } from '/@/api-services/models/career-guidance-video-editing-input';
import Editor from '/@/components/editor/index.vue';

// Props
interface Props {
	visible: boolean;
	editData?: CareerGuidanceOutput | null;
	title?: string;
	type: number;
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	editData: null,
	title: '',
	type: 1,
});

// Emits
const emit = defineEmits<{
	'update:visible': [value: boolean];
	success: [];
}>();

// Refs
const formRef = ref<FormInstance>();
const uploadRef = ref();
const imageRef = ref();

// Reactive data
const dialogVisible = computed({
	get: () => props.visible,
	set: (value) => emit('update:visible', value),
});

const isEdit = computed(() => !!props.editData?.id);

const formData = reactive({
	id: undefined as number | undefined,
	type: 1,
	title: '',
	lecturer: '',
	classHours: '',
	image: '',
	imageFile: null as File | null,
	videos: [] as Array<CareerGuidanceVideoEditingInput>,
	shortCourseIntroduction: '',
	courseIntroduction: '',
	isPublished: false,
	publishTime: null as Date | null,
});

const fileList = ref<UploadFile[]>([]);
const imageUrl = ref('');
const submitLoading = ref(false);

// Form validation rules
const rules = computed<FormRules>(() => {
	return {
		title: [
			{ required: true, message: '请输入标题', trigger: 'blur' },
			{ min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' },
		],
		lecturer: [{ required: true, message: '请输入讲师姓名', trigger: 'blur' }],
		image: [
			{ 
				required: !isEdit.value, 
				message: '请上传封面图片', 
				trigger: 'change',
				validator: (rule: any, value: any, callback: any) => {
					// 新增时必须有图片文件
					if (!isEdit.value && !formData.imageFile) {
						callback(new Error('请上传封面图片'));
						return;
					}
					// 编辑时如果没有新图片文件，必须有原有图片URL
					if (isEdit.value && !formData.imageFile && !formData.image) {
						callback(new Error('请上传封面图片'));
						return;
					}
					callback();
				}
			}
		],
	};
});

// Watch for edit data changes
watch(
	() => props.editData,
	async (newData) => {
		if (newData && props.visible) {
			await loadEditData(newData);
		}
	},
	{ immediate: true }
);

// Watch for dialog visibility
watch(() => props.visible, (visible) => {
	if (visible && !props.editData) {
		resetForm();
	}
});

// Watch for type changes
watch(
	() => props.type,
	(newType) => {
		formData.type = newType;
	},
	{ immediate: true }
);

// Methods
const resetForm = () => {
	formData.id = undefined;
	formData.type = props.type;
	formData.title = '';
	formData.lecturer = '';
	formData.classHours = '';
	formData.image = '';
	formData.imageFile = null;
	formData.videos = [];
	formData.shortCourseIntroduction = '';
	formData.courseIntroduction = '';
	formData.isPublished = false;
	formData.publishTime = null;
	fileList.value = [];
	imageUrl.value = '';

	nextTick(() => {
		formRef.value?.clearValidate();
	});
};

const loadEditData = async (editData: CareerGuidanceOutput) => {
	const data = editData;

	if (data) {
		formData.id = data.id;
		formData.type = data.type || props.type;
		formData.title = data.title || '';
		formData.lecturer = data.lecturer || '';
		formData.classHours = data.classHours || '';
		formData.image = data.image || '';
		formData.videos = data.videos?.map((video) => ({
			id: video.id,
			careerGuidanceId: video.careerGuidanceId,
			videoTitle: video.videoTitle || '',
			videoLink: video.videoLink || '',
			sort: video.sort || 1,
		})) || [];
		formData.shortCourseIntroduction = data.shortCourseIntroduction || '';
		formData.courseIntroduction = data.courseIntroduction || '';
		formData.isPublished = data.isPublished || false;

		// 设置时间为 Date 对象
		formData.publishTime = data.publishTime ? new Date(data.publishTime) : null;

		// Set image preview
		if (data.image) {
			imageUrl.value = data.image;
		}
	}
};

// 更新待办事项状态

const handleClick = (e: MouseEvent) => {
	e.stopPropagation();
	e.preventDefault();
	imageRef.value!.showPreview();
};

const handleFileChange = (file: UploadFile) => {
	if (file.raw) {
		formData.imageFile = file.raw;

		// Create preview URL
		const reader = new FileReader();
		reader.onload = (e) => {
			imageUrl.value = e.target?.result as string;
		};
		reader.readAsDataURL(file.raw);

		// Clear validation error and trigger validation
		nextTick(() => {
			formRef.value?.validateField('image');
		});
	}
};

const beforeUpload = (file: File) => {
	const isImage = file.type.startsWith('image/');
	const isLt2M = file.size / 1024 / 1024 < 2;

	if (!isImage) {
		ElMessage.error('只能上传图片文件!');
		return false;
	}
	if (!isLt2M) {
		ElMessage.error('图片大小不能超过 2MB!');
		return false;
	}
	return true;
};

const handleReupload = () => {
	uploadRef.value?.$el.querySelector('input').click();
};

const handleDeleteImage = () => {
	ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		imageUrl.value = '';
		formData.imageFile = null;
		formData.image = '';
		fileList.value = [];
		
		// 触发验证检查
		nextTick(() => {
			formRef.value?.validateField('image');
		});
	}).catch(() => {
		// User cancelled
	});
};

const addVideo = () => {
	formData.videos.push({
		videoTitle: '',
		videoLink: '',
		sort: formData.videos.length + 1,
	});
};

const removeVideo = (index: number) => {
	formData.videos.splice(index, 1);
};

const handleSubmit = async () => {
	if (!formRef.value) return;

	try {
		await formRef.value.validate();

		// Validate image upload - 新增时必须有图片文件，编辑时如果有新图片文件或原有图片URL即可
		if (!isEdit.value && !formData.imageFile) {
			ElMessage.error('请上传封面图片');
			return;
		}
		
		if (isEdit.value && !formData.imageFile && !formData.image) {
			ElMessage.error('请上传封面图片');
			return;
		}

		submitLoading.value = true;

		// 使用API接口提交
		await getAPI(CareerGuidanceApi).apiCareerGuidanceSavePostForm(
			isEdit.value ? formData.id : undefined,
			formData.type,
			formData.title,
			formData.classHours,
			formData.lecturer,
			formData.imageFile ? '' : formData.image, // 如果有新文件，image参数传空字符串
			formData.imageFile || undefined,
			formData.videos,
			formData.shortCourseIntroduction,
			formData.courseIntroduction,
			formData.isPublished,
			formData.publishTime ? formData.publishTime.toLocaleString() : undefined
		);

		ElMessage.success(isEdit.value ? '更新成功' : '保存成功');
		emit('success');
		handleClose();
	} catch (error) {
		console.error('提交失败:', error);
		ElMessage.error('提交失败，请重试');
	} finally {
		submitLoading.value = false;
	}
};

const handleClose = () => {
	dialogVisible.value = false;
	resetForm();
};
</script>

<style lang="scss" scoped>
.edit-dialog {
	.dialog-form {
		padding: 0 20px;
	}

	.dialog-footer {
		text-align: right;

		.el-button {
			margin-left: 10px;
		}
	}

	.video-list {
		width: 100%;

		.video-item {
			margin-bottom: 10px;
		}
	}

	.upload-container {
		width: 150px;
		height: 150px;
		position: relative;

		.image-preview {
			width: 100%;
			height: 100%;
			position: relative;
			border-radius: 6px;
			overflow: hidden;

			.el-image {
				width: 100%;
				height: 100%;
			}

			&:hover .image-overlay {
				opacity: 1;
			}

			.image-overlay {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: rgba(0, 0, 0, 0.5);
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 10px;
				opacity: 0;
				transition: opacity 0.3s;

				.upload-icon,
				.delete-icon {
					color: #fff;
					cursor: pointer;
					font-size: 16px;

					&:hover {
						color: #409eff;
					}
				}

				.delete-icon:hover {
					color: #f56c6c;
				}
			}
		}

		.image-upload {
			width: 100%;
			height: 100%;

			:deep(.el-upload) {
				width: 100%;
				height: 100%;
				border: 1px dashed #d9d9d9;
				border-radius: 6px;
				cursor: pointer;
				position: relative;
				overflow: hidden;
				transition: all 0.3s;

				&:hover {
					border-color: #409eff;
				}
			}

			.upload-placeholder {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: #8c939d;

				.upload-icon {
					font-size: 28px;
					margin-bottom: 8px;
				}

				.upload-text {
					font-size: 14px;
					margin-bottom: 4px;
				}

				.upload-tip {
					font-size: 12px;
					color: #a8abb2;
				}
			}
		}
	}
}
</style>