<template>
	<div class="recruitment-live-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="直播标题">
					<el-input v-model="state.queryParams.title" placeholder="直播标题" clearable />
				</el-form-item>
				<el-form-item label="直播时间">
					<el-date-picker
						v-model="state.queryParams.startTimeRange"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						clearable
					/>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddLive"> 新增 </el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.liveData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="title" label="直播带岗标题" min-width="300" align="left" show-overflow-tooltip />
				<el-table-column label="直播时间" width="160" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.startTime) }}
					</template>
				</el-table-column>
				<el-table-column label="发布时间" width="160" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.createTime) }}
					</template>
				</el-table-column>
				<el-table-column prop="createUserName" label="发布者" width="120" align="center" show-overflow-tooltip />
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditLive(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="delLive(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				size="small"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>

		<EditDialog
			v-model:visible="state.dialogVisible"
			:edit-data="state.editData"
			:title="state.editLiveTitle"
			@success="handleQuery"
		/>
	</div>
</template>

<script lang="ts" setup name="recruitmentLive">
import { onMounted, reactive } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditDialog from './components/editDialog.vue';
import { getAPI } from '/@/utils/axios-utils';
import { RecruitmentLiveApi } from '/@/api-services/api';
import { RecruitmentLiveOutput } from '/@/api-services/models/recruitment-live-output';
import { formatDate } from '/@/utils/formatTime';

const state = reactive({
	loading: false,
	liveData: [] as Array<RecruitmentLiveOutput>,
	queryParams: {
		title: undefined,
		startTimeRange: [] as string[],
	},
	tableParams: {
		page: 1,
		pageSize: 50,
		total: 0 as any,
	},
	editLiveTitle: '',
	dialogVisible: false,
	editData: null as RecruitmentLiveOutput | null,
});

onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign({}, state.queryParams, state.tableParams);
		
		// 处理时间范围参数
		let startTimeBegin: Date | undefined;
		let startTimeEnd: Date | undefined;
		if (params.startTimeRange && params.startTimeRange.length === 2) {
			startTimeBegin = new Date(params.startTimeRange[0]);
			startTimeEnd = new Date(params.startTimeRange[1]);
		}

		let res = await getAPI(RecruitmentLiveApi).apiRecruitmentLivePageGet(
			params.title,
			startTimeBegin,
			startTimeEnd,
			params.page,
			params.pageSize
		);
		state.liveData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
		ElMessage.error('查询失败');
	} finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.title = undefined;
	state.queryParams.startTimeRange = [];
	await handleQuery();
};

// 打开新增页面
const openAddLive = () => {
	state.editLiveTitle = '添加直播带岗';
	state.editData = null;
	state.dialogVisible = true;
};

// 打开编辑页面
const openEditLive = async (row: any) => {
	state.editLiveTitle = '编辑直播带岗';
	try {
		const res = await getAPI(RecruitmentLiveApi).apiRecruitmentLiveDetailIdGet(row.id);
		state.editData = res.data.result;
		state.dialogVisible = true;
	} catch (error) {
		ElMessage.error('获取直播详情失败');
	}
};

// 删除
const delLive = (row: any) => {
	ElMessageBox.confirm(`确定删除直播带岗：【${row.title}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(RecruitmentLiveApi).apiRecruitmentLiveDeleteIdDelete(row.id);
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = async (val: number) => {
	state.tableParams.pageSize = val;
	await handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 格式化日期
const formatDateTime = (date: any) => {
	if (!date) return '-';
	return formatDate(new Date(date), 'YYYY-mm-dd HH:MM:SS');
};
</script>

<style lang="scss" scoped>
.recruitment-live-container {
    padding: 20px;
}
</style>