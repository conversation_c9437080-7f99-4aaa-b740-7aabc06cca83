<template>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑直播带岗' : '新增直播带岗'" width="900px" :before-close="handleClose"
        class="edit-dialog">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="dialog-form">
            <el-form-item label="直播标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入直播标题" clearable maxlength="100" show-word-limit />
            </el-form-item>

            <el-form-item label="直播时间" prop="startTime">
                <el-date-picker
                    v-model="formData.startTime"
                    type="datetime"
                    placeholder="请选择直播开始时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    style="width: 49%; margin-right: 2%"
                />
                <el-date-picker
                    v-model="formData.endTime"
                    type="datetime"
                    placeholder="请选择直播结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    style="width: 49%"
                />
            </el-form-item>

            <el-form-item label="直播图片" prop="coverImageFile">
                <div class="upload-container">
                    <div v-if="imageUrl" class="image-preview">
                        <el-image ref="imageRef" :src="imageUrl" alt="预览图片" :fit="'fill'"
                            :preview-src-list="[imageUrl]" />
                        <div class="image-overlay">
                            <el-icon class="upload-icon" size="20" @click.stop="handleClick">
                                <View />
                            </el-icon>
                            <el-icon class="upload-icon" size="20" @click.stop="handleReupload">
                                <Upload />
                            </el-icon>
                            <el-icon class="delete-icon" size="20" @click.stop="handleDeleteImage">
                                <Delete />
                            </el-icon>
                        </div>
                    </div>
                    <el-upload ref="uploadRef" v-else :file-list="fileList" :auto-upload="false" :show-file-list="false"
                        :on-change="handleFileChange" :before-upload="beforeUpload" accept="image/*"
                        class="image-upload">
                        <div class="upload-placeholder">
                            <el-icon class="upload-icon">
                                <Plus />
                            </el-icon>
                            <div class="upload-text">点击上传图片</div>
                            <div class="upload-tip">支持 jpg、png、gif 格式，大小不超过 5MB</div>
                        </div>
                    </el-upload>
                </div>
            </el-form-item>

            <el-form-item label="直播介绍" prop="briefIntroduction">
                <Editor v-model:get-html="formData.briefIntroduction" height="300px" />
            </el-form-item>

            <el-form-item label="招聘企业" prop="enterprises">
                <Editor v-model:get-html="formData.enterprises" height="300px" />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
                    {{ isEdit ? '更新' : '保存' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, type UploadFile } from 'element-plus'
import { Plus, Upload, Delete, View } from '@element-plus/icons-vue'
import { RecruitmentLiveApi } from '/@/api-services/api'
import { getAPI } from '/@/utils/axios-utils'
import { RecruitmentLiveOutput } from '/@/api-services/models/recruitment-live-output'
import Editor from '/@/components/editor/index.vue'

// Props
interface Props {
    visible: boolean
    editData?: RecruitmentLiveOutput | null
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    editData: null
})

// Emits
const emit = defineEmits<{
    'update:visible': [value: boolean]
    'success': []
}>()

// Refs
const formRef = ref<FormInstance>()
const uploadRef = ref()
const imageRef = ref()

// Reactive data
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.editData?.id)

const formData = reactive({
    id: undefined as number | undefined,
    title: '',
    startTime: null as Date | null,
    endTime: null as Date | null,
    coverImage: '',
    coverImageFile: null as File | null,
    briefIntroduction: '',
    enterprises: ''
})

const fileList = ref<UploadFile[]>([])
const imageUrl = ref('')
const submitLoading = ref(false)

// Form validation rules
const rules = computed<FormRules>(() => {
    return {
        title: [
            { required: true, message: '请输入直播标题', trigger: 'blur' },
            { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        startTime: [
            { required: true, message: '请选择直播开始时间', trigger: 'change' }
        ],
        endTime: [
            { required: true, message: '请选择直播结束时间', trigger: 'change' },
            {
                validator: (rule: any, value: Date, callback: any) => {
                    if (value && formData.startTime && value <= formData.startTime) {
                        callback(new Error('结束时间必须大于开始时间'))
                    } else {
                        callback()
                    }
                },
                trigger: 'change'
            }
        ],
        coverImageFile: [
            { required: isEdit.value ? false : true, message: '请上传直播图片', trigger: 'change' }
        ]
    }
})

// Watch for edit data changes
watch(() => props.editData, async (newData) => {
    if (newData && props.visible) {
        await loadEditData(newData)
    }
}, { immediate: true })

// Watch for dialog visibility
watch(() => props.visible, (visible) => {
    if (visible && !props.editData) {
        resetForm()
    }
})

// Watch for start time changes to revalidate end time
watch(() => formData.startTime, () => {
    if (formData.endTime) {
        nextTick(() => {
            formRef.value?.validateField('endTime')
        })
    }
})

// Methods
const formatDateTime = (date: Date | null): string | undefined => {
    if (!date) return undefined
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hours = date.getHours()
    const minutes = date.getMinutes()
    const seconds = date.getSeconds()
    
    return `${year}-${month}-${day} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

const resetForm = () => {
    formData.id = undefined
    formData.title = ''
    formData.startTime = null
    formData.endTime = null
    formData.coverImage = ''
    formData.coverImageFile = null
    formData.briefIntroduction = ''
    formData.enterprises = ''
    fileList.value = []
    imageUrl.value = ''

    nextTick(() => {
        formRef.value?.clearValidate()
    })
}

const loadEditData = async (editData: RecruitmentLiveOutput) => {
    const data = editData

    if (data) {
        formData.id = data.id
        formData.title = data.title || ''
        formData.coverImage = data.coverImage || ''
        formData.briefIntroduction = data.briefIntroduction || ''
        formData.enterprises = data.enterprises || ''
        
        // 设置时间为 Date 对象
        formData.startTime = data.startTime ? new Date(data.startTime) : null
        formData.endTime = data.endTime ? new Date(data.endTime) : null

        // Set image preview
        if (data.coverImage) {
            imageUrl.value = data.coverImage
        }
    }
}

const handleClick = (e: MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
    imageRef.value!.showPreview()
}

const handleFileChange = (file: UploadFile) => {
    if (file.raw) {
        formData.coverImageFile = file.raw

        // Create preview URL
        const reader = new FileReader()
        reader.onload = (e) => {
            imageUrl.value = e.target?.result as string
        }
        reader.readAsDataURL(file.raw)

        // Clear validation error
        formRef.value?.clearValidate('coverImageFile')
    }
}

const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    const isLt5M = file.size / 1024 / 1024 < 5

    if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
    }
    if (!isLt5M) {
        ElMessage.error('图片大小不能超过 5MB!')
        return false
    }
    return true
}

const handleReupload = () => {
    uploadRef.value?.$el.querySelector('input').click()
}

const handleDeleteImage = () => {
    ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        imageUrl.value = ''
        formData.coverImageFile = null
        formData.coverImage = ''
        fileList.value = []
    }).catch(() => {
        // User cancelled
    })
}

const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()

        // Validate image upload for new records or when image is changed
        if (!isEdit.value && !formData.coverImageFile) {
            ElMessage.error('请上传直播图片')
            return
        }

        submitLoading.value = true

        await getAPI(RecruitmentLiveApi).apiRecruitmentLiveSubmitPostForm(
            formData.id,
            formData.title,
            formatDateTime(formData.startTime),
            formatDateTime(formData.endTime),
            formData.coverImageFile as Blob,
            formData.coverImage,
            formData.briefIntroduction,
            formData.enterprises
        )

        ElMessage.success(isEdit.value ? '更新成功' : '保存成功')
        emit('success')
        handleClose()
    } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败，请重试')
    } finally {
        submitLoading.value = false
    }
}

const handleClose = () => {
    dialogVisible.value = false
    resetForm()
}
</script>

<style lang="scss" scoped>
.edit-dialog {
    .dialog-form {
        padding: 0 20px;
    }

    .dialog-footer {
        text-align: right;

        .el-button {
            margin-left: 10px;
        }
    }
}

.upload-container {
    width: 150px;
    height: 150px;
    position: relative;

    .image-preview {
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 6px;
        overflow: hidden;

        .el-image {
            width: 100%;
            height: 100%;
        }

        &:hover .image-overlay {
            opacity: 1;
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            opacity: 0;
            transition: opacity 0.3s;

            .upload-icon,
            .delete-icon {
                color: #fff;
                cursor: pointer;
                font-size: 16px;

                &:hover {
                    color: #409eff;
                }
            }

            .delete-icon:hover {
                color: #f56c6c;
            }
        }
    }

    .image-upload {
        width: 100%;
        height: 100%;

        :deep(.el-upload) {
            width: 100%;
            height: 100%;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s;

            &:hover {
                border-color: #409eff;
            }
        }

        .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #8c939d;

            .upload-icon {
                font-size: 28px;
                margin-bottom: 8px;
            }

            .upload-text {
                font-size: 14px;
                margin-bottom: 4px;
            }

            .upload-tip {
                font-size: 12px;
                color: #a8abb2;
            }
        }
    }
}
</style>