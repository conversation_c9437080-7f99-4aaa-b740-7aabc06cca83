<template>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑宣传图片' : '新增宣传图片'" width="600px" :before-close="handleClose"
        class="add-dialog">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="dialog-form">
            <el-form-item label="图片标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入图片标题" clearable maxlength="100" show-word-limit />
            </el-form-item>

            <el-form-item label="图片位置" prop="location">
                <el-select v-model="formData.location" placeholder="请选择图片位置" style="width: 100%">
                    <el-option v-for="item in locationOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="上线时间" prop="onlineTime">
                <el-date-picker v-model="formData.onlineTime" type="datetimerange" range-separator="至"
                    start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>


            <el-form-item label="宣传图片" prop="urlPathFile">
                <div class="upload-container">
                    <div v-if="imageUrl" class="image-preview">
                        <el-image ref="imageRef" :src="imageUrl" alt="预览图片" :fit="'fill'"
                            :preview-src-list="[imageUrl]" />
                        <div class="image-overlay">
                            <el-icon class="upload-icon" size="20" @click.stop="handleClick">
                                <View />
                            </el-icon>
                            <el-icon class="upload-icon" size="20" @click.stop="handleReupload">
                                <Upload />
                            </el-icon>
                            <el-icon class="delete-icon" size="20" @click.stop="handleDeleteImage">
                                <Delete />
                            </el-icon>
                        </div>
                    </div>
                    <el-upload ref="uploadRef" v-else :file-list="fileList" :auto-upload="false" :show-file-list="false"
                        :on-change="handleFileChange" :before-upload="beforeUpload" accept="image/*"
                        class="image-upload">

                        <div class="upload-placeholder">
                            <el-icon class="upload-icon">
                                <Plus />
                            </el-icon>
                            <div class="upload-text">点击上传图片</div>
                            <div class="upload-tip">支持 jpg、png、gif 格式，大小不超过 5MB</div>
                        </div>
                    </el-upload>
                </div>
            </el-form-item>

            <el-form-item label="跳转链接" prop="link">
                <el-input v-model="formData.link" placeholder="请输入跳转链接（可选）" clearable maxlength="500" show-word-limit />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
                    {{ isEdit ? '更新' : '保存' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, type UploadFile } from 'element-plus'
import { Plus, Upload, Delete, View } from '@element-plus/icons-vue'
import { PropagandaPictureApi } from '/@/api-services/api'
import { getAPI } from '/@/utils/axios-utils'
import { PropagandaPictureOutput } from '/@/api-services/models/propaganda-picture-output'
import { PropagandaPictureLocation } from '/@/api-services/models/propaganda-picture-location'

// Props
interface Props {
    visible: boolean
    editData?: PropagandaPictureOutput | null
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    editData: null
})

const imageRef = ref()
const handleClick = (e: MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
    imageRef.value!.showPreview()
}
// Emits
const emit = defineEmits<{
    'update:visible': [value: boolean]
    'success': []
}>()

// Refs
const formRef = ref<FormInstance>()
const uploadRef = ref()

// Reactive data
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.editData?.id)

const formData = reactive({
    id: undefined as number | undefined,
    title: '',
    location: undefined as number | undefined,
    onlineTime: [] as string[],
    urlPath: '',
    urlPathFile: null as File | null,
    link: ''
})

const fileList = ref<UploadFile[]>([])
const imageUrl = ref('')
const submitLoading = ref(false)

// Location options
const locationOptions = [
    { label: 'PC首页', value: PropagandaPictureLocation.NUMBER_0 },
    { label: '小程序首页', value: PropagandaPictureLocation.NUMBER_1 },
    { label: 'PC职业指导页', value: PropagandaPictureLocation.NUMBER_2 },
    { label: '小程序职业指导页', value: PropagandaPictureLocation.NUMBER_3 }
]

// Form validation rules
const rules = computed<FormRules>(()=>{
    return {
    title: [
        { required: true, message: '请输入图片标题', trigger: 'blur' },
        { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
    ],
    location: [
        { required: true, message: '请选择图片位置', trigger: 'change' }
    ],
    onlineTime: [
        { required: true, message: '请选择上线时间', trigger: 'change' }
    ],
    urlPathFile: [
        { required:isEdit.value ? false : true, message: '请上传宣传图片', trigger: 'change' }
    ]
}
})

// Watch for edit data changes
watch(() => props.editData, async (newData) => {
    if (newData && props.visible) {
        await loadEditData(newData)
    }
}, { immediate: true })

// Watch for dialog visibility
watch(() => props.visible, (visible) => {
    if (visible && !props.editData) {
        resetForm()
    }
})

// Methods
const resetForm = () => {
    formData.id = undefined
    formData.title = ''
    formData.location = undefined
    formData.onlineTime = []
    formData.urlPath = ''
    formData.urlPathFile = null
    formData.link = ''
    fileList.value = []
    imageUrl.value = ''

    nextTick(() => {
        formRef.value?.clearValidate()
    })
}

const loadEditData = async (editData: PropagandaPictureOutput) => {

    const data = editData

    console.log(data);

    if (data) {
        formData.id = data.id
        formData.title = data.title || ''
        formData.location = data.location
        formData.onlineTime = data.onlineStartTime && data.onlineEndTime
            ? [
                data.onlineStartTime,
                data.onlineEndTime
            ]
            : []
        formData.urlPath = data.url || ''
        formData.link = data.link || ''

        // Set image preview
        if (data.url) {
            imageUrl.value = data.url
        }
    }

}

const handleFileChange = (file: UploadFile) => {
    if (file.raw) {
        formData.urlPathFile = file.raw

        // Create preview URL
        const reader = new FileReader()
        reader.onload = (e) => {
            imageUrl.value = e.target?.result as string
        }
        reader.readAsDataURL(file.raw)

        // Clear validation error
        formRef.value?.clearValidate('urlPathFile')
    }
}

const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    const isLt5M = file.size / 1024 / 1024 < 5

    if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
    }
    if (!isLt5M) {
        ElMessage.error('图片大小不能超过 5MB!')
        return false
    }
    return true
}

const handleReupload = () => {
    uploadRef.value?.$el.querySelector('input').click()
}

const handleDeleteImage = () => {
    ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        imageUrl.value = ''
        formData.urlPathFile = null
        formData.urlPath = ''
        fileList.value = []
    }).catch(() => {
        // User cancelled
    })
}

const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()

        // Validate image upload for new records or when image is changed
        if (!isEdit.value && !formData.urlPathFile) {
            ElMessage.error('请上传宣传图片')
            return
        }

        submitLoading.value = true

        const [onlineStartTime, onlineEndTime] = formData.onlineTime

        await getAPI(PropagandaPictureApi).apiPropagandaPictureSubmitPostForm(
            formData.id,
            formData.title,
            formData.location,
            onlineStartTime,
            onlineEndTime,
            formData.urlPath,
            formData.urlPathFile as Blob,
            formData.link
        )

        ElMessage.success(isEdit.value ? '更新成功' : '保存成功')
        emit('success')
        handleClose()
    } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败，请重试')
    } finally {
        submitLoading.value = false
    }
}

const handleClose = () => {
    dialogVisible.value = false
    resetForm()
}
</script>

<style lang="scss" scoped>
.add-dialog {
    .dialog-form {
        padding: 0 20px;

        .upload-container {
            .image-preview {
                    position: relative;
                    width: 200px;
                    height: 120px;

                    :deep(.el-image) {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        border-radius: 6px;
                    }

                    .image-overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 20px;
                        opacity: 0;
                        transition: opacity 0.3s;
                        border-radius: 6px;

                        .upload-icon,
                        .delete-icon {
                            color: #fff;
                            display: inline-block;

                            &:hover {
                                color: var(--el-color-primary);
                            }
                        }
                    }

                    &:hover .image-overlay {
                        opacity: 1;
                    }
                }
            .image-upload {
                :deep(.el-upload) {
                    border: 1px dashed var(--el-border-color);
                    border-radius: 6px;
                    cursor: pointer;
                    position: relative;
                    overflow: hidden;
                    transition: var(--el-transition-duration-fast);

                    &:hover {
                        border-color: var(--el-color-primary);
                    }
                }

           

                .upload-placeholder {
                    width: 200px;
                    height: 120px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: var(--el-text-color-secondary);

                    .upload-icon {
                        font-size: 28px;
                        margin-bottom: 8px;
                    }

                    .upload-text {
                        font-size: 14px;
                        margin-bottom: 4px;
                    }

                    .upload-tip {
                        font-size: 12px;
                        color: var(--el-text-color-placeholder);
                    }
                }
            }
        }
    }

    .dialog-footer {
        text-align: right;

        .el-button {
            margin-left: 10px;
        }
    }
}
</style>