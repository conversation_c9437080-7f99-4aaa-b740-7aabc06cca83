<template>
    <div class="sys-oplog-container">
        <Pagination ref="paginationRef" :defaultSort="{}" :showSelection="true" :showSerialNo="false"
            v-model:columns="tableColList" :option="post" :asyncOption="asyncPost" :ApiFunction="ApiFunction"
            :affixTarget="'.sys-oplog-container'">

            <template #form="{ data }">

                <Formcol>
                    <FormLabel>
                        <template #label> 名称:</template>
                        <el-input v-model="asyncPost.title" placeholder="请输入宣传图片名称" clearable />
                    </FormLabel>
                </Formcol>

            </template>
            <template #headerLeft>
                <Formcol>
                    <FormLabel>
                        <template #label>
                            <el-button :type="'primary'" @click="search" :icon="Search"> 查询</el-button><el-button
                                :icon="Delete" @click="reset"> 重置</el-button>

                                <el-button :icon="Plus" @click="add" >新增</el-button>
                        </template>
                    </FormLabel>
                </Formcol>
            </template>
            <template #image="{ row }">
               <div style="position: relative;">
                <el-image :preview-teleported="true" :src="row.url" :fit="'fill'" :preview-src-list="[row.url]" />
               </div>
            </template>
            <template #action="{ row }">
                <el-button :icon="Edit" @click="edit(row)" >编辑</el-button>
                <el-button :icon="Delete" @click="del(row)" type="danger" >删除</el-button>
            </template>
        </Pagination>

        <!-- 新增/编辑弹框 -->
        <AddDialog
            v-model:visible="dialogVisible"
            :edit-data="editData"
            @success="handleDialogSuccess"
        />
    </div>
</template>

<script lang="ts" setup name="sysOpLog">
import { onMounted, reactive, defineAsyncComponent, ref } from 'vue';
import { PropagandaPictureApi } from '/@/api-services/api';
import { Delete, Plus, Search, Share, Upload, InfoFilled, FolderAdd, Edit, Files, ArrowDown } from '@element-plus/icons-vue';
import { getAPI } from '/@/utils/axios-utils';
import Formcol from '/@/components/table/formcol.vue';
import FormLabel from '/@/components/table/formLabel.vue';
import { PropagandaPictureOutput } from '/@/api-services/models/propaganda-picture-output';
import { PropagandaPictureLocation } from '/@/api-services/models/propaganda-picture-location';
import AddDialog from './components/addDialog.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { deepClone } from '/@/utils/arrayOperation';
const ApiFunction = async ({ title, page, pageSize }: { title?: string, page?: number, pageSize?: number }) => {
    return await getAPI(PropagandaPictureApi).apiPropagandaPicturePageGet(title, page, pageSize);
}

const Pagination = defineAsyncComponent(() => import('/@/components/table/pagination.vue'));
const post = reactive({
  
});
const asyncPost = ref({
    title: '',
});

// 弹框相关状态
const dialogVisible = ref(false);
const editData = ref<PropagandaPictureOutput | null>(null);

onMounted(async () => {

});

const paginationRef = ref();

const search = () => {
    paginationRef.value.asyncOptionReset();
};

const reset = () => {
    let { asyncOption } = paginationRef.value.getOriginalOption();
    console.log(asyncOption);

    for (let k in asyncPost.value) {
        (asyncPost.value as any)[k] = asyncOption[k];
    }
    search();
};

const add = () => {
    editData.value = null;
    dialogVisible.value = true;
}

const edit = (row: PropagandaPictureOutput) => {
    editData.value = deepClone(row);
    dialogVisible.value = true;
}

const del = async (row: PropagandaPictureOutput) => {
    try {
        await ElMessageBox.confirm('确定要删除这条宣传图片吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        await getAPI(PropagandaPictureApi).apiPropagandaPictureDeleteIdDelete(row.id!);
        ElMessage.success('删除成功');
        search(); // 刷新列表
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请重试');
        }
    }
}

const handleDialogSuccess = () => {
    // 刷新列表
    search();
}

const locationMachning = (location: PropagandaPictureLocation) => {
    let l = {
        0: 'PC首页',
        1: '小程序首页',
        2: '小程序职业指导页',
        3: 'PC职业指导页',
    }
    return l[location as keyof typeof l];
}

const timeMachning = (_: PropagandaPictureLocation, row: PropagandaPictureOutput) => {
    return row.onlineStartTime + ' - ' + row.onlineEndTime;
}
const tableColList = ref([
    { prop: 'title', label: '宣传图片名称', align: 'center', isCheck: true, hideCheck: true },
    { prop: 'url', slot: 'image',width:150, label: '图片', align: 'center', isCheck: true, hideCheck: true },
    { prop: 'location', machining: locationMachning, label: '图片位置', headerAlign: 'center', align: 'center', toolTip: true, isCheck: true },
    { prop: 'onlineStartTime', machining: timeMachning,width:300, label: '上线时间', headerAlign: 'center', align: 'center', isCheck: true },
    { prop: 'createUserName', label: '发布者', align: 'center', isCheck: true },
    { prop: '', label: '操作', slot: 'action', align: 'center', isCheck: true, fixed: 'right', hideCheck: true },
])
</script>

<style lang="scss" scoped>
.el-popper {
    max-width: 60%;
}

pre {
    white-space: break-spaces;
    line-height: 20px;
}

.el-table .warning-row {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-table .success-row {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
