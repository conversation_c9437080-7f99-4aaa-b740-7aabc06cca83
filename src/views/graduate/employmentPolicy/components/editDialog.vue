<template>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑就业政策文章' : '新增就业政策文章'" width="900px" :before-close="handleClose"
        class="edit-dialog">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="dialog-form">
            <el-form-item label="文章标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入文章标题" clearable maxlength="100" show-word-limit />
            </el-form-item>

            <el-form-item label="发布时间" prop="publishTime">
                <el-date-picker
                    v-model="formData.publishTime"
                    type="datetime"
                    placeholder="请选择发布时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                />
            </el-form-item>

            <el-form-item label="文章内容" prop="content">
                <Editor v-model:get-html="formData.content" height="400px" />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
                    {{ isEdit ? '更新' : '保存' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { EmploymentPolicyApi } from '/@/api-services/api'
import { getAPI } from '/@/utils/axios-utils'
import { EmploymentPolicyArticleOutput } from '/@/api-services/models/employment-policy-article-output'
import { EmploymentPolicyArticleEditingInput } from '/@/api-services/models/employment-policy-article-editing-input'
import Editor from '/@/components/editor/index.vue'

// Props
interface Props {
    visible: boolean
    editData?: EmploymentPolicyArticleOutput | null
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    editData: null
})

// Emits
const emit = defineEmits<{
    'update:visible': [value: boolean]
    'success': []
}>()

// Refs
const formRef = ref<FormInstance>()

// Reactive data
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.editData?.id)

const formData = reactive({
    id: undefined as number | undefined,
    title: '',
    content: '',
    publishTime: ''
})

const submitLoading = ref(false)

// Form validation rules
const rules = computed<FormRules>(() => {
    return {
        title: [
            { required: true, message: '请输入文章标题', trigger: 'blur' },
            { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        publishTime: [
            { required: true, message: '请选择发布时间', trigger: 'change' }
        ]
    }
})

// Watch for edit data changes
watch(() => props.editData, async (newData) => {
    if (newData && props.visible) {
        await loadEditData(newData)
    }
}, { immediate: true })

// Watch for dialog visibility
watch(() => props.visible, (visible) => {
    if (visible && !props.editData) {
        resetForm()
    }
})

// Methods
const resetForm = () => {
    formData.id = undefined
    formData.title = ''
    formData.content = ''
    formData.publishTime = ''

    nextTick(() => {
        formRef.value?.clearValidate()
    })
}

const loadEditData = async (editData: EmploymentPolicyArticleOutput) => {
    const data = editData

    if (data) {
        formData.id = data.id
        formData.title = data.title || ''
        formData.content = data.content || ''
        
        // 格式化发布时间
        if (data.publishTime) {
            const date = new Date(data.publishTime)
            formData.publishTime = date.toISOString().slice(0, 19).replace('T', ' ')
        } else {
            formData.publishTime = ''
        }
    }
}

const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()

        submitLoading.value = true

        const submitData: EmploymentPolicyArticleEditingInput = {
            title: formData.title,
            content: formData.content,
            publishTime: new Date(formData.publishTime)
        }

        if (isEdit.value && formData.id) {
            submitData.id = formData.id
        }

        await getAPI(EmploymentPolicyApi).apiEmploymentPolicySubmitPost(submitData)

        ElMessage.success(isEdit.value ? '更新成功' : '保存成功')
        emit('success')
        handleClose()
    } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败，请重试')
    } finally {
        submitLoading.value = false
    }
}

const handleClose = () => {
    dialogVisible.value = false
    resetForm()
}
</script>

<style lang="scss" scoped>
.edit-dialog {
    .dialog-form {
        padding: 0 20px;
    }

    .dialog-footer {
        text-align: right;

        .el-button {
            margin-left: 10px;
        }
    }
}
</style>