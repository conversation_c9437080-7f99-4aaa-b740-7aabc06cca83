<template>
	<div class="employment-policy-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="就业政策标题">
					<el-input v-model="state.queryParams.title" placeholder="就业政策标题" clearable />
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddPolicy"> 新增 </el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.policyData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="title" label="就业政策标题" min-width="300" align="left" show-overflow-tooltip />
				<el-table-column label="发布时间" width="180" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.publishTime) }}
					</template>
				</el-table-column>
				<el-table-column prop="createUserName" label="发布者" width="120" align="center" show-overflow-tooltip />
				<el-table-column label="创建时间" width="110" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.createdTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditPolicy(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="delPolicy(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				size="small"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>

		<EditDialog
			v-model:visible="state.dialogVisible"
			:edit-data="state.editData"
			:title="state.editPolicyTitle"
			@success="handleQuery"
		/>
	</div>
</template>

<script lang="ts" setup name="employmentPolicy">
import { onMounted, reactive, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditDialog from './components/editDialog.vue';
import { getAPI } from '/@/utils/axios-utils';
import { EmploymentPolicyApi } from '/@/api-services/api';
import { EmploymentPolicyArticleOutput } from '/@/api-services/models/employment-policy-article-output';
import { formatDate } from '/@/utils/formatTime';

const state = reactive({
	loading: false,
	policyData: [] as Array<EmploymentPolicyArticleOutput>,
	queryParams: {
		title: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 50,
		total: 0 as any,
	},
	editPolicyTitle: '',
	dialogVisible: false,
	editData: null as EmploymentPolicyArticleOutput | null,
});

onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign({}, state.queryParams, state.tableParams);
		let res = await getAPI(EmploymentPolicyApi).apiEmploymentPolicyPageGet(
			params.title,
			params.page,
			params.pageSize
		);
		state.policyData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
		ElMessage.error('查询失败');
	} finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.title = undefined;
	await handleQuery();
};

// 打开新增页面
const openAddPolicy = () => {
	state.editPolicyTitle = '添加就业政策';
	state.editData = null;
	state.dialogVisible = true;
};

// 打开编辑页面
const openEditPolicy = async (row: any) => {
	state.editPolicyTitle = '编辑就业政策';
	try {
		const res = await getAPI(EmploymentPolicyApi).apiEmploymentPolicyDetailIdGet(row.id);
		state.editData = res.data.result;
		state.dialogVisible = true;
	} catch (error) {
		ElMessage.error('获取就业政策详情失败');
	}
};

// 删除
const delPolicy = (row: any) => {
	ElMessageBox.confirm(`确定删除就业政策：【${row.title}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(EmploymentPolicyApi).apiEmploymentPolicyDeleteIdDelete(row.id);
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = async (val: number) => {
	state.tableParams.pageSize = val;
	await handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 格式化日期
const formatDateTime = (date: any) => {
	if (!date) return '-';
	return formatDate(new Date(date), 'YYYY-mm-dd');
};
</script>

<style lang="scss" scoped>
.employment-policy-container {
    padding: 20px;
}
</style>