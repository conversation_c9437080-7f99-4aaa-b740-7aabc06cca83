<template>
	<div class="system-user-container layout-padding">
		<el-card shadow="hover" class="layout-padding-auto">
			<div class="system-user-search mb15">
				<el-form :model="state.tableData.param" ref="queryRef" :inline="true">
					<el-form-item label="大学" prop="universityId">
						<el-select v-model="state.tableData.param.universityId" placeholder="请选择大学" clearable class="w-100">
							<el-option v-for="item in state.universityList" :key="item.id" :label="item.name" :value="item.id" />
						</el-select>
					</el-form-item>
					<el-form-item label="图片类别" prop="category">
						<el-select v-model="state.tableData.param.category" placeholder="请选择图片类别" clearable class="w-100">
							<el-option label="Logo" :value="0" />
							<el-option label="小程序形象照片" :value="1" />
							<el-option label="触控端形象照片" :value="2" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" class="ml10" @click="getTableData">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button @click="resetQuery">
							<el-icon>
								<ele-Refresh />
							</el-icon>
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</div>
			<el-row :gutter="15" class="mb15">
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
					<el-button type="primary" class="ml10" @click="onOpenEditImage">
						<el-icon>
							<ele-FolderAdd />
						</el-icon>
						上传图片
					</el-button>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12"> </el-col>
			</el-row>
			<el-table :data="state.tableData.data" style="width: 100%" v-loading="state.tableData.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="universityName" label="大学名称" min-width="100" align="center" />
				<el-table-column label="图片预览" width="120" align="center">
					<template #default="scope">
						<el-image :src="scope.row.url || ''" fit="cover" style="width: 80px; height: 60px; border-radius: 4px" :preview-src-list="scope.row.url ? [scope.row.url] : []" preview-teleported>
							<template #error>
								<div class="image-slot">
									<el-icon><ele-Picture /></el-icon>
								</div>
							</template>
						</el-image>
					</template>
				</el-table-column>
				<el-table-column prop="category" label="图片类别" width="110" align="center">
					<template #default="scope">
						{{ getCategoryLabel(scope.row.category) }}
					</template>
				</el-table-column>
				<el-table-column label="创建时间" width="140" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.createdTime || '' }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="onEditImage(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="onDeleteImage(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<div v-if="state.tableData.data.length === 0 && !state.tableData.loading" class="empty-state">
				<el-empty description="暂无图片数据" />
			</div>
			<el-pagination
				@size-change="onHandleSizeChange"
				@current-change="onHandleCurrentChange"
				class="mt15"
				:pager-count="5"
				:page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.pageNum"
				background
				v-model:page-size="state.tableData.param.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total"
			>
			</el-pagination>
		</el-card>
		<EditUniversityImage ref="editUniversityImageRef" @refresh="getTableData" :universityList="state.universityList" />
	</div>
</template>

<script setup lang="ts" name="systemUniversityImage">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { UniversityImageApi, UniversityApi } from '/@/api-services';
import { UniversityImageOutput, UniversityOutput } from '/@/api-services/models';
import { formatDate } from '/@/utils/formatTime';

// 引入组件
const EditUniversityImage = defineAsyncComponent(() => import('./component/editUniversityImage.vue'));

// 定义变量内容
const editUniversityImageRef = ref<InstanceType<typeof EditUniversityImage>>();
const queryRef = ref();
const state = reactive({
	tableData: {
		data: [] as UniversityImageOutput[],
		total: 0,
		loading: false,
		param: {
			universityId: undefined as number | undefined,
			category: undefined as number | undefined,
			pageNum: 1,
			pageSize: 20,
		},
	},
	universityList: [] as UniversityOutput[],
});

// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	try {
		const res = await getAPI(UniversityImageApi).apiUniversityImagePageGet(
			state.tableData.param.universityId,
			state.tableData.param.category,
			undefined, // url
			state.tableData.param.pageNum,
			state.tableData.param.pageSize
		);
		console.log('API Response:', res.data);
		state.tableData.data = res.data.result?.items || [];
		state.tableData.total = res.data.result?.total || 0;
		console.log('Table Data:', state.tableData.data);
		console.log('Total Count:', state.tableData.total);
	} catch (error) {
		console.error('获取图片列表失败:', error);
		ElMessage.error('获取图片列表失败');
	}
	state.tableData.loading = false;
};

// 获取大学列表
const getUniversityList = async () => {
	try {
		const res = await getAPI(UniversityApi).apiUniversityPageGet(undefined, undefined, undefined, undefined, 1, 1000);
		state.universityList = res.data.result?.items || [];
	} catch (error) {
		console.error('获取大学列表失败:', error);
	}
};

// 获取图片类别标签
const getCategoryLabel = (category: number | undefined) => {
	switch (category) {
		case 0:
			return 'Logo';
		case 1:
			return '小程序形象照片';
		case 2:
			return '触控端形象照片';
		default:
			return '未知';
	}
};

// 打开新增图片弹窗
const onOpenEditImage = () => {
	//ElMessage.info('新增功能暂未实现');
	editUniversityImageRef.value?.openDialog();
};

// 打开编辑图片弹窗
const onEditImage = (row: UniversityImageOutput) => {
	editUniversityImageRef.value?.openDialog(row);
};

// 预览图片 - 已通过 el-image 的 preview-src-list 实现

// 删除图片
const onDeleteImage = (row: UniversityImageOutput) => {
	ElMessageBox.confirm(`确定要删除这张图片吗?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(UniversityImageApi).apiUniversityImageDeleteIdDelete(row.id!);
				ElMessage.success('删除成功');
				getTableData();
			} catch (error) {
				console.error('删除失败:', error);
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};

// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};

// 重置查询
const resetQuery = () => {
	queryRef.value?.resetFields();
	getTableData();
};

// 页面加载时
onMounted(() => {
	getTableData();
	getUniversityList();
});
</script>

<style scoped lang="scss">
.system-user-container {
	.image-slot {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 80px;
		height: 60px;
		background: var(--el-fill-color-light);
		color: var(--el-text-color-secondary);
		font-size: 24px;
		border-radius: 4px;
	}

	.empty-state {
		margin: 40px 0;
	}
}
</style>
