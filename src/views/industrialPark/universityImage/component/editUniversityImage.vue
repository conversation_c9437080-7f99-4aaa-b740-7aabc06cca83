<template>
	<div class="system-edit-university-image-container">
		<el-dialog  v-model="state.dialog.isShowDialog" width="60%">
		    <template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>
					<span>{{ state.dialog.title }}</span>
			</div>
			</template>
			<el-form
				:model="state.ruleForm"
				:rules="state.rules"
				ref="ruleFormRef"
				label-width="120px"
				class="demo-ruleForm"
				size="default"
				status-icon
			>
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="关联大学" prop="universityId">
							<el-select
								v-model="state.ruleForm.universityId"
								placeholder="请选择大学"
								clearable
								class="w-100"
							>
								<el-option
									v-for="item in props.universityList"
									:key="item.id"
									:label="item.name"
									:value="item.id"
								/>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="图片类别" prop="category">
							<el-select
								v-model="state.ruleForm.category"
								placeholder="请选择图片类别"
								clearable
								class="w-100"
							>
								<el-option label="Logo" :value="0" />
								<el-option label="小程序形象照" :value="1" />
								<el-option label="触控端形象照" :value="2" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="上传方式">
							<el-radio-group v-model="state.uploadMode">
								<el-radio :label="'file'">文件上传</el-radio>
								<el-radio :label="'url'">URL链接</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-if="state.uploadMode === 'file'" class="mb20">
						<el-form-item label="图片文件" prop="imageFile">
							<div class="upload-container">
								<el-upload
									ref="uploadRef"
									class="image-uploader"
									:show-file-list="false"
									:before-upload="beforeUpload"
									:on-change="handleFileChange"
									:auto-upload="false"
									accept="image/*"
									drag
								>
									<div v-if="state.imagePreview" class="image-preview">
										<img :src="state.imagePreview" alt="preview" />
										<div class="image-actions">
											<el-button
												type="primary"
												size="small"
												@click.stop="cropImage"
											>
												<el-icon><ele-Edit /></el-icon>
												裁剪
											</el-button>
											<el-button
												type="danger"
												size="small"
												@click.stop="removeImage"
											>
												<el-icon><ele-Delete /></el-icon>
												删除
											</el-button>
										</div>
									</div>
									<div v-else class="upload-placeholder">
										<el-icon class="upload-icon"><ele-Plus /></el-icon>
										<div class="upload-text">点击或拖拽上传图片</div>
										<div class="upload-tips">支持 JPG、PNG、GIF 格式，文件大小不超过 5MB</div>
									</div>
								</el-upload>
							</div>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-if="state.uploadMode === 'url'" class="mb20">
						<el-form-item label="图片URL" prop="url">
							<el-input
								v-model="state.ruleForm.url"
								placeholder="请输入图片URL地址"
								clearable
								@blur="previewUrlImage"
							/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-if="state.uploadMode === 'url' && state.ruleForm.url" class="mb20">
						<el-form-item label="URL预览">
							<div class="url-preview">
								<el-image
									:src="state.ruleForm.url"
									fit="contain"
									style="width: 200px; height: 150px; border: 1px solid var(--el-border-color-light)"
								>
									<template #error>
										<div class="image-slot">
											<el-icon><ele-Picture /></el-icon>
											<div>加载失败</div>
										</div>
									</template>
								</el-image>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit" size="default" :loading="state.loading">
						{{ state.loading ? '提交中' : '确 定' }}
					</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 图片裁剪弹窗 -->
		<Cropper ref="cropperRef" title="图片裁剪" @uploadCropperImg="handleCroppedImage" />
	</div>
</template>

<script setup lang="ts" name="systemEditUniversityImage">
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, type UploadFile } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { UniversityImageApi, UniversityApi } from '/@/api-services';
import { UniversityImageOutput, UniversityOutput } from '/@/api-services/models';
import Cropper from '/@/components/cropper/index.vue';
const props = defineProps<{
	title: string;
	universityList: Array<UniversityOutput>;
}>();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const ruleFormRef = ref<HTMLElement>();
const uploadRef = ref<HTMLElement>();
const cropperRef = ref<InstanceType<typeof Cropper>>();

// 定义变量内容
const state = reactive({
	loading: false,
	uploadMode: 'file' as 'file' | 'url',
	imagePreview: '',
	currentFile: null as File | null,
	ruleForm: {
		id: undefined as number | undefined,
		universityId: undefined as number | undefined,
		category: undefined as number | undefined,
		imageFile: undefined as File | undefined,
		url: '',
	},
	dialog: {
		isShowDialog: false,
		title: '',
	},
	rules: {
		universityId: [{ required: true, message: '请选择关联大学', trigger: 'change' }],
		category: [{ required: true, message: '请选择图片类别', trigger: 'change' }],
		imageFile: [{ required: true, validator: validateImageFile, trigger: 'change' }],
		url: [{ required: true, message: '请输入图片URL', trigger: 'blur' }],
	},
});

// 文件上传验证
function validateImageFile(rule: any, value: any, callback: any) {
	if (state.uploadMode === 'file') {
		if (!state.currentFile && !state.imagePreview) {
			callback(new Error('请上传图片文件'));
		} else {
			callback();
		}
	} else {
		callback();
	}
}



// 打开弹窗
const openDialog = (row?: UniversityImageOutput) => {
	resetForm();
	if (row) {
		state.dialog.title = '编辑图片';
		state.ruleForm.id = row.id;
		state.ruleForm.universityId = row.universityId;
		state.ruleForm.category = row.category;
		if (row.url) {
			state.uploadMode = 'url';
			state.ruleForm.url = row.url;
		}
	} else {
		state.dialog.title = '上传图片';
		state.uploadMode = 'file';
	}
	state.dialog.isShowDialog = true;
	
};

// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
	resetForm();
};

// 重置表单
const resetForm = () => {
	state.ruleForm = {
		id: undefined,
		universityId: undefined,
		category: undefined,
		imageFile: undefined,
		url: '',
	};
	state.imagePreview = '';
	state.currentFile = null;
	state.uploadMode = 'file';
	state.loading = false;
};

// 取消
const onCancel = () => {
	closeDialog();
};

// 文件上传前验证
const beforeUpload = (file: File) => {
	const isImage = file.type.startsWith('image/');
	const isLt5M = file.size / 1024 / 1024 < 5;

	if (!isImage) {
		ElMessage.error('只能上传图片文件！');
		return false;
	}
	if (!isLt5M) {
		ElMessage.error('图片大小不能超过 5MB！');
		return false;
	}
	return false; // 阻止自动上传
};

// 文件选择变化
const handleFileChange = (file: UploadFile) => {
	if (file.raw) {
		state.currentFile = file.raw;
		const reader = new FileReader();
		reader.onload = (e) => {
			state.imagePreview = e.target?.result as string;
		};
		reader.readAsDataURL(file.raw);
	}
};

// 删除图片
const removeImage = () => {
	state.imagePreview = '';
	state.currentFile = null;
	state.ruleForm.imageFile = undefined;
};

// 裁剪图片
const cropImage = () => {
	if (state.imagePreview) {
		cropperRef.value?.openDialog(state.imagePreview);
	}
};

// 处理裁剪后的图片
const handleCroppedImage = (data: { img: string }) => {
	// 将base64转换为Blob
	const base64 = data.img;
	const arr = base64.split(',');
	const mime = arr[0].match(/:(.*?);/)![1];
	const bstr = atob(arr[1]);
	let n = bstr.length;
	const u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	const blob = new Blob([u8arr], { type: mime });
	const file = new File([blob], 'cropped.jpg', { type: mime });
	
	state.currentFile = file;
	state.imagePreview = base64;
	state.ruleForm.imageFile = file;
};

// 预览URL图片
const previewUrlImage = () => {
	// URL预览通过 el-image 组件自动实现
};

// 提交
const onSubmit = async () => {
	const form = ruleFormRef.value as any;
	if (!form) return;

	await form.validate(async (valid: boolean) => {
		if (!valid) return;

		state.loading = true;
		try {
			// API调用会自动处理FormData
			await getAPI(UniversityImageApi).apiUniversityImageSubmitPostForm(
				state.ruleForm.id,
				state.ruleForm.universityId,
				state.ruleForm.category,
				state.uploadMode === 'file' ? state.currentFile || undefined : undefined,
				state.uploadMode === 'url' ? state.ruleForm.url : undefined
			);

			ElMessage.success('操作成功');
			closeDialog();
			emit('refresh');
		} catch (error) {
			console.error('提交失败:', error);
			ElMessage.error('操作失败');
		}
		state.loading = false;
	});
};

// 暴露变量
defineExpose({
	openDialog,
});


</script>

<style scoped lang="scss">
.system-edit-university-image-container {
	.upload-container {
		width: 100%;

		.image-uploader {
			width: 100%;

			:deep(.el-upload) {
				border: 1px dashed var(--el-border-color);
				border-radius: 6px;
				cursor: pointer;
				position: relative;
				overflow: hidden;
				transition: var(--el-transition-duration-fast);
				width: 100%;
				height: 300px;

				&:hover {
					border-color: var(--el-color-primary);
				}
			}

			:deep(.el-upload-dragger) {
				width: 100%;
				height: 100%;
				background: transparent;
				border: none;
				border-radius: 6px;
			}
		}

		.image-preview {
			position: relative;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;

			img {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
			}

			.image-actions {
				position: absolute;
				top: 10px;
				right: 10px;
				display: flex;
				gap: 8px;
			}
		}

		.upload-placeholder {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 100%;
			color: var(--el-text-color-secondary);

			.upload-icon {
				font-size: 67px;
				color: var(--el-text-color-placeholder);
				margin-bottom: 16px;
			}

			.upload-text {
				font-size: 16px;
				margin-bottom: 8px;
			}

			.upload-tips {
				font-size: 12px;
				color: var(--el-text-color-placeholder);
			}
		}
	}

	.url-preview {
		.image-slot {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100%;
			background: var(--el-fill-color-light);
			color: var(--el-text-color-secondary);
			font-size: 14px;

			.el-icon {
				font-size: 30px;
				margin-bottom: 8px;
			}
		}
	}
}
</style>