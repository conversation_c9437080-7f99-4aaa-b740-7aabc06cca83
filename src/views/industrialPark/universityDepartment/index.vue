<template>
	<div class="university-department-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="大学">
					<el-select v-model="state.queryParams.universityId" placeholder="请选择大学" clearable style="width: 200px" @change="handleQuery" filterable>
						<el-option v-for="item in state.universityList" :key="item.id" :label="item.name" :value="item.id" />
					</el-select>
				</el-form-item>
				<el-form-item label="院系名称">
					<el-input v-model="state.queryParams.name" placeholder="院系名称" clearable />
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddUniversityDepartment"> 新增 </el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.universityDepartmentData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="universityName" label="大学名称" min-width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="name" label="院系名称" min-width="120" align="center" show-overflow-tooltip />
				<el-table-column prop="numberOfGraduates" label="毕业生数量" width="110" align="center" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.numberOfGraduates !== undefined && scope.row.numberOfGraduates !== null">
							{{ scope.row.numberOfGraduates }}
						</span>
						<span v-else style="color: #999">-</span>
					</template>
				</el-table-column>
				<el-table-column prop="numberOfInterns" label="实习生数量" width="110" align="center" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.numberOfInterns !== undefined && scope.row.numberOfInterns !== null">
							{{ scope.row.numberOfInterns }}
						</span>
						<span v-else style="color: #999">-</span>
					</template>
				</el-table-column>
				<!-- <el-table-column prop="description" label="院系简介" min-width="200" align="center" show-overflow-tooltip /> -->
				<el-table-column label="创建时间" width="110" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.createdTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditUniversityDepartment(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="delUniversityDepartment(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				size="small"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>

		<EditUniversityDepartment ref="editUniversityDepartmentRef" :title="state.editUniversityDepartmentTitle" @handleQuery="handleQuery" :universityList="state.universityList" />
	</div>
</template>

<script lang="ts" setup name="universityDepartment">
import { onMounted, reactive, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditUniversityDepartment from './component/editUniversityDepartment.vue';
import { getAPI } from '/@/utils/axios-utils';
import { UniversityDepartmentApi, UniversityApi } from '/@/api-services/api';
import { UniversityDepartmentOutput, UniversityOutput } from '/@/api-services/models';
import { formatDate } from '/@/utils/formatTime';

const editUniversityDepartmentRef = ref<InstanceType<typeof EditUniversityDepartment>>();
const state = reactive({
	loading: false,
	universityDepartmentData: [] as Array<UniversityDepartmentOutput>,
	universityList: [] as Array<UniversityOutput>,
	queryParams: {
		universityId: undefined,
		name: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 50,
		total: 0 as any,
	},
	editUniversityDepartmentTitle: '',
});

onMounted(async () => {
	await loadUniversityList();
	await handleQuery();
});

// 加载大学列表
const loadUniversityList = async () => {
	try {
		const res = await getAPI(UniversityApi).apiUniversityPageGet(undefined, undefined, undefined, undefined, 1, 1000);
		state.universityList = res.data.result?.items ?? [];
	} catch (error) {
		ElMessage.error('加载大学列表失败');
	}
};

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign({}, state.queryParams, state.tableParams);
		let res = await getAPI(UniversityDepartmentApi).apiUniversityDepartmentPageGet(
			params.universityId,
			params.name,
			params.page,
			params.pageSize
		);
		state.universityDepartmentData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
		ElMessage.error('查询失败');
	} finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.universityId = undefined;
	state.queryParams.name = undefined;
	await handleQuery();
};

// 打开新增页面
const openAddUniversityDepartment = () => {
	state.editUniversityDepartmentTitle = '添加院系';
	editUniversityDepartmentRef.value?.openDialog({});
};

// 打开编辑页面
const openEditUniversityDepartment = async (row: any) => {
	state.editUniversityDepartmentTitle = '编辑院系';
	try {
		const res = await getAPI(UniversityDepartmentApi).apiUniversityDepartmentDetailIdGet(row.id);
		editUniversityDepartmentRef.value?.openDialog(res.data.result);
	} catch (error) {
		ElMessage.error('获取院系详情失败');
	}
};

// 删除
const delUniversityDepartment = (row: any) => {
	ElMessageBox.confirm(`确定删除院系：【${row.name}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(UniversityDepartmentApi).apiUniversityDepartmentDeleteIdDelete(row.id);
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = async (val: number) => {
	state.tableParams.pageSize = val;
	await handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 格式化日期
const formatDateTime = (date: any) => {
	if (!date) return '-';
	return formatDate(new Date(date), 'YYYY-mm-dd');
};
</script>