<template>
	<div class="edit-university-department-container">
		<el-dialog v-model="state.isShowDialog" draggable :close-on-click-modal="false" width="60%">
			<template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="120px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="所属大学" prop="universityId">
							<el-select v-model="state.ruleForm.universityId" placeholder="请选择大学" clearable style="width: 100%" filterable>
								<el-option v-for="item in props.universityList" :key="item.id" :label="item.name" :value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="院系名称" prop="name">
							<el-input v-model="state.ruleForm.name" placeholder="请输入院系名称" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="毕业生数量" prop="numberOfGraduates">
							<el-input-number 
								v-model="state.ruleForm.numberOfGraduates" 
								placeholder="实习生数量" 
								class="w100" 
								:min="0"
								controls-position="right"
							/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="专科毕业生数量" prop="numberOfInterns">
							<el-input-number 
								v-model="state.ruleForm.numberOfInterns" 
								placeholder="专科毕业生数量" 
								class="w100" 
								:min="0"
								controls-position="right"
							/>
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="院系简介">
							<el-input 
								v-model="state.ruleForm.description" 
								placeholder="请输入院系简介" 
								type="textarea" 
								:rows="4" 
								clearable 
							/>
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item>
							<el-alert title="数据说明" type="info" show-icon :closable="false">
								<template #default>
									<div>• 本科毕业生数量：统计该院系本科层次的毕业生人数</div>
									<div>• 专科毕业生数量：统计该院系专科层次的毕业生人数</div>
									<div>• 院系简介：可详细描述院系的专业设置、师资力量、办学特色等信息</div>
								</template>
							</el-alert>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="editUniversityDepartment">
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { UniversityDepartmentApi } from '/@/api-services/api';
import { UniversityDepartmentEditingInput, UniversityOutput } from '/@/api-services/models';

const props = defineProps<{
	title: string;
	universityList: Array<UniversityOutput>;
}>();
const emits = defineEmits(['handleQuery']);
const ruleFormRef = ref();
const state = reactive({
	loading: false,
	isShowDialog: false,
	ruleForm: {} as UniversityDepartmentEditingInput,
});

// 表单验证规则
const rules = {
	universityId: [{ required: true, message: '请选择所属大学', trigger: 'change' }],
	name: [{ required: true, message: '院系名称不能为空', trigger: 'blur' }],
	numberOfGraduates: [
		{
			validator: (rule: any, value: any, callback: any) => {
				if (value !== undefined && value !== null && value < 0) {
					callback(new Error('本科毕业生数量不能为负数'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		},
		{required: true, message: '请输入本科毕业生数量', trigger: 'blur'}
	],
	numberOfInterns: [
		{
			validator: (rule: any, value: any, callback: any) => {
				if (value !== undefined && value !== null && value < 0) {
					callback(new Error('专科毕业生数量不能为负数'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		},
		{required: true, message: '请输入专科毕业生数量', trigger: 'blur'}
	],
};

// 打开弹窗
const openDialog = (row: any) => {
	ruleFormRef.value?.resetFields();
	state.ruleForm = {
		id: row?.id || null,
		universityId: row?.universityId || undefined,
		name: row?.name || '',
		numberOfGraduates: row?.numberOfGraduates || undefined,
		numberOfInterns: row?.numberOfInterns || undefined
	};
	state.isShowDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	closeDialog();
};

// 提交
const submit = async () => {
	ruleFormRef.value?.validate(async (valid: boolean) => {
		if (!valid) return;
		
		state.loading = true;
		try {
			await getAPI(UniversityDepartmentApi).apiUniversityDepartmentSubmitPost(state.ruleForm);
			ElMessage.success('操作成功');
			emits('handleQuery');
			closeDialog();
		} catch (error) {
			ElMessage.error('操作失败');
		} finally {
			state.loading = false;
		}
	});
};

// 暴露方法给父组件调用
defineExpose({
	openDialog,
});
</script>

<style scoped>
.w100 {
	width: 100%;
}
</style>