<template>
	<div class="university-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="大学名称">
					<el-input v-model="state.queryParams.name" placeholder="大学名称" clearable />
				</el-form-item>
				<el-form-item label="高等教育分类">
					<el-select v-model="state.queryParams.typeOfHigherEducation" placeholder="高等教育分类" clearable>
						<el-option label="专科" :value="0" />
						<el-option label="本科" :value="1" />
					</el-select>
				</el-form-item>
				<el-form-item label="高校类别">
					<el-select v-model="state.queryParams.typeOfInstitutions" placeholder="高校类别" clearable>
						<el-option label="综合类" :value="0" />
						<el-option label="师范类" :value="1" />
						<el-option label="理工类" :value="2" />
						<el-option label="农林类" :value="3" />
						<el-option label="医药类" :value="4" />
						<el-option label="财经类" :value="5" />
						<el-option label="政法类" :value="6" />
						<el-option label="艺术类" :value="7" />
						<el-option label="体育类" :value="8" />
					</el-select>
				</el-form-item>
				<el-form-item label="重点大学分类">
					<el-select v-model="state.queryParams.typeOfKeyUniversity" placeholder="重点大学分类" clearable>
						<el-option label="民办" :value="0" />
						<el-option label="公办" :value="1" />
						<el-option label="211" :value="2" />
						<el-option label="985" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddUniversity"> 新增 </el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.universityData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="name" label="大学名称" min-width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="briefIntroduction" label="简介" min-width="150" align="center" show-overflow-tooltip />
				<el-table-column label="高等教育分类" width="120" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.typeOfHigherEducation === 0" type="info">专科</el-tag>
						<el-tag v-else-if="scope.row.typeOfHigherEducation === 1" type="success">本科</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="高校类别" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.typeOfInstitutions === 0">综合类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 1">师范类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 2">理工类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 3">农林类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 4">医药类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 5">财经类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 6">政法类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 7">艺术类</span>
						<span v-else-if="scope.row.typeOfInstitutions === 8">体育类</span>
					</template>
				</el-table-column>
				<el-table-column label="重点大学分类" width="120" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.typeOfKeyUniversity === 0" type="info">民办</el-tag>
						<el-tag v-else-if="scope.row.typeOfKeyUniversity === 1" type="primary">公办</el-tag>
						<el-tag v-else-if="scope.row.typeOfKeyUniversity === 2" type="warning">211</el-tag>
						<el-tag v-else-if="scope.row.typeOfKeyUniversity === 3" type="danger">985</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="所属产业园" width="180" align="center" show-overflow-tooltip>
					<template #default="scope">
						<div v-for="item in scope.row.industrialParkArr" :key="item">{{ getIndustrialParkName(item) }}</div>
					</template>
				</el-table-column>
				<el-table-column prop="numberOfMajors" label="专业数量" width="100" align="center" show-overflow-tooltip />
				<el-table-column prop="numberOfUndergraduateGraduates" label="本科及以上毕业生" width="110" align="center" show-overflow-tooltip />
				<el-table-column prop="numberOfJuniorCollegeGraduates" label="专科毕业生" width="110" align="center" show-overflow-tooltip />
				<el-table-column prop="addressCount" label="校区数" width="80" align="center" show-overflow-tooltip />
				<el-table-column prop="departmentCount" label="院系数" width="80" align="center" show-overflow-tooltip />
				<el-table-column label="创建时间" width="110" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.createdTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditUniversity(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="delUniversity(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				size="small"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>

		<EditUniversity ref="editUniversityRef" :title="state.editUniversityTitle" @handleQuery="handleQuery" :industrialParkList="state.industrialParkList" />
	</div>
</template>

<script lang="ts" setup name="university">
import { onMounted, reactive, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditUniversity from './component/editUniversity.vue';
import { getAPI } from '/@/utils/axios-utils';
import { IndustrialParkDataApi, UniversityApi } from '/@/api-services/api';
import { IndustrialParkBaseInfoOutput, UniversityOutput } from '/@/api-services/models';
import { formatDate } from '/@/utils/formatTime';

const editUniversityRef = ref<InstanceType<typeof EditUniversity>>();
const state = reactive({
	loading: false,
	universityData: [] as Array<UniversityOutput>,
	queryParams: {
		name: undefined,
		typeOfHigherEducation: undefined,
		typeOfInstitutions: undefined,
		typeOfKeyUniversity: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 50,
		total: 0 as any,
	},
	editUniversityTitle: '',
	industrialParkList: [] as Array<IndustrialParkBaseInfoOutput>,
});

onMounted(async () => {
	await loadIndustrialParkList();
	await handleQuery();
});
// 加载产业园列表
const loadIndustrialParkList = async () => {
	try {
		// 先获取现有数据中的产业园信息
		const res = await getAPI(UniversityApi).apiUniversityParkListGet();
		
		const items = res.data.result ?? [];
		
		state.industrialParkList = items;
	} catch (error) {
		console.error('加载产业园列表失败:', error);
	}
};
//获取产业园名称
const getIndustrialParkName = (id: number) => {
	return state.industrialParkList.find(item => item.id === id)?.name;
};

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign({}, state.queryParams, state.tableParams);
		let res = await getAPI(UniversityApi).apiUniversityPageGet(
			params.name,
			params.typeOfHigherEducation,
			params.typeOfInstitutions,
			params.typeOfKeyUniversity,
			params.page,
			params.pageSize
		);
		state.universityData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
		ElMessage.error('查询失败');
	} finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.name = undefined;
	state.queryParams.typeOfHigherEducation = undefined;
	state.queryParams.typeOfInstitutions = undefined;
	state.queryParams.typeOfKeyUniversity = undefined;
	await handleQuery();
};

// 打开新增页面
const openAddUniversity = () => {
	state.editUniversityTitle = '添加大学';
	editUniversityRef.value?.openDialog({});
};

// 打开编辑页面
const openEditUniversity = async (row: any) => {
	state.editUniversityTitle = '编辑大学';
	try {
		const res = await getAPI(UniversityApi).apiUniversityDetailIdGet(row.id);
		editUniversityRef.value?.openDialog(res.data.result);
	} catch (error) {
		ElMessage.error('获取大学详情失败');
	}
};

// 删除
const delUniversity = (row: any) => {
	ElMessageBox.confirm(`确定删除大学：【${row.name}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(UniversityApi).apiUniversityDeleteIdDelete(row.id);
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = async (val: number) => {
	state.tableParams.pageSize = val;
	await handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 格式化日期
const formatDateTime = (date: any) => {
	if (!date) return '-';
	return formatDate(new Date(date), 'YYYY-mm-dd');
};
</script>