<template>
	<div class="edit-university-container">
		<el-dialog v-model="state.isShowDialog" draggable :close-on-click-modal="false" width="60%">
			<template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="120px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="大学名称" prop="name">
							<el-input v-model="state.ruleForm.name" placeholder="请输入大学名称" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="高等教育分类" prop="typeOfHigherEducation">
							<el-select v-model="state.ruleForm.typeOfHigherEducation" placeholder="请选择高等教育分类" clearable style="width: 100%">
								<el-option label="专科" :value="0" />
								<el-option label="本科" :value="1" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="高校类别" prop="typeOfInstitutions">
							<el-select v-model="state.ruleForm.typeOfInstitutions" placeholder="请选择高校类别" clearable style="width: 100%">
								<el-option label="综合类" :value="0" />
								<el-option label="师范类" :value="1" />
								<el-option label="理工类" :value="2" />
								<el-option label="农林类" :value="3" />
								<el-option label="医药类" :value="4" />
								<el-option label="财经类" :value="5" />
								<el-option label="政法类" :value="6" />
								<el-option label="艺术类" :value="7" />
								<el-option label="体育类" :value="8" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="重点大学分类" prop="typeOfKeyUniversity">
							<el-select v-model="state.ruleForm.typeOfKeyUniversity" placeholder="请选择重点大学分类" clearable style="width: 100%">
								<el-option label="民办" :value="0" />
								<el-option label="公办" :value="1" />
								<el-option label="211" :value="2" />
								<el-option label="985" :value="3" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
						<el-form-item label="开设专业数量" prop="numberOfMajors">
							<el-input-number v-model="state.ruleForm.numberOfMajors" placeholder="专业数量" class="w100" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
						<el-form-item label="本科及以上毕业生数量" prop="numberOfUndergraduateGraduates">
							<el-input-number v-model="state.ruleForm.numberOfUndergraduateGraduates" placeholder="本科毕业生数量" class="w100" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
						<el-form-item label="专科毕业生数量" prop="numberOfJuniorCollegeGraduates">
							<el-input-number v-model="state.ruleForm.numberOfJuniorCollegeGraduates" placeholder="专科毕业生数量" class="w100" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb20">
						<el-form-item label="所属产业园" prop="industrialParkIds">
							<el-select v-model="state.ruleForm.industrialParkIds" placeholder="请选择所属产业园" multiple clearable style="width: 100%">
								<el-option v-for="item in props.industrialParkList" :key="item.id" :label="item.name" :value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="简介" prop="briefIntroduction">
							<el-input 
								v-model="state.ruleForm.briefIntroduction" 
								placeholder="请输入大学简介" 
								type="textarea" 
								:rows="4" 
								clearable 
								maxlength="200"
								show-word-limit
							/>
						</el-form-item>
					</el-col>
					
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="editUniversity">
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { UniversityApi } from '/@/api-services/api';
import { UniversityEditingInput } from '/@/api-services/models';

const props = defineProps({
	title: String,
	industrialParkList: {
		type: Array as () => Array<{ id: number; name: string }>,
		default: () => []
	}
});
const emits = defineEmits(['handleQuery']);
const ruleFormRef = ref();
const state = reactive({
	loading: false,
	isShowDialog: false,
	ruleForm: {} as UniversityEditingInput,
});
const checkIndustrialParkIds = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请选择所属园区'))
  } else if (value.length === 0) {
    callback(new Error("请选择所属园区"))
  } else {
    callback()
  }
};
// 表单验证规则
const rules = {
	name: [{ required: true, message: '大学名称不能为空', trigger: 'blur' }],
	briefIntroduction: [
		{ required: true, message: '大学简介不能为空', trigger: 'blur' },
		{ max: 200, message: '大学简介不能超过200字', trigger: 'blur' }
	],
	typeOfHigherEducation: [{ required: true, message: '高等教育分类不能为空', trigger: 'blur' }],
	typeOfInstitutions: [{ required: true, message: '高校类别不能为空', trigger: 'blur' }],
	typeOfKeyUniversity: [{ required: true, message: '重点大学分类不能为空', trigger: 'blur' }],
	numberOfMajors: [{ required: true, message: '开设专业数量不能为空', trigger: 'blur' }],
	numberOfUndergraduateGraduates: [{ required: true, message: '本科毕业生数量不能为空', trigger: 'blur' }],
	numberOfJuniorCollegeGraduates: [{ required: true, message: '专科毕业生数量不能为空', trigger: 'blur' }],
	industrialParkIds: [{validator: checkIndustrialParkIds, trigger: 'blur'},{required: true, message: '请选择所属园区', trigger: 'blur'}],
};


// 打开弹窗
const openDialog = (row: any) => {
	ruleFormRef.value?.resetFields();
	state.ruleForm = {
		id: row?.id || null,
		name: row?.name || '',
		briefIntroduction: row?.briefIntroduction || '',
		typeOfHigherEducation: row?.typeOfHigherEducation,
		typeOfInstitutions: row?.typeOfInstitutions,
		typeOfKeyUniversity: row?.typeOfKeyUniversity,
		numberOfMajors: row?.numberOfMajors || 0,
		numberOfUndergraduateGraduates: row?.numberOfUndergraduateGraduates || 0,
		numberOfJuniorCollegeGraduates: row?.numberOfJuniorCollegeGraduates || 0,
		industrialParkIds: row?.industrialParkArr || [],
	};
	state.isShowDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	closeDialog();
};

// 提交
const submit = async () => {
	ruleFormRef.value?.validate(async (valid: boolean) => {
		if (!valid) return;
		
		state.loading = true;
		try {
			await getAPI(UniversityApi).apiUniversitySubmitPost(state.ruleForm);
			ElMessage.success('操作成功');
			emits('handleQuery');
			closeDialog();
		} catch (error) {
			ElMessage.error('操作失败');
		} finally {
			state.loading = false;
		}
	});
};

// 暴露方法给父组件调用
defineExpose({
	openDialog,
});
</script>

<style scoped>
.w100 {
	width: 100%;
}
</style>