<template>
	<div class="edit-university-address-container">
		<el-dialog v-model="state.isShowDialog" draggable :close-on-click-modal="false" width="60%">
			<template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="120px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="所属大学" prop="universityId">
							<el-select v-model="state.ruleForm.universityId" placeholder="请选择大学" clearable style="width: 100%" filterable>
								<el-option v-for="item in props.universityList" :key="item.id" :label="item.name" :value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="校区名称" prop="campus">
							<el-input v-model="state.ruleForm.campus" placeholder="请输入校区名称" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="详细地址" prop="address">
							<el-input v-model="state.ruleForm.address" placeholder="请输入详细地址" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="经度" prop="longitude">
							<el-input-number 
								v-model="state.ruleForm.longitude" 
								placeholder="经度" 
								class="w100" 
								:precision="6"
								:min="-180"
								:max="180"
								controls-position="right"
							/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="纬度" prop="latitude">
							<el-input-number 
								v-model="state.ruleForm.latitude" 
								placeholder="纬度" 
								class="w100" 
								:precision="6"
								:min="-90"
								:max="90"
								controls-position="right"
							/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item>
							<el-alert title="经纬度获取提示" type="info" show-icon :closable="false">
								<template #default>
									<div>经纬度可通过以下方式获取：</div>
									<div>1. 使用百度地图、高德地图等在线地图工具获取</div>
									<div>2. 使用GPS设备或手机定位获取</div>
									<div>3. 中国境内经度范围：73°-135°，纬度范围：18°-54°</div>
								</template>
							</el-alert>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="editUniversityAddress">
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { UniversityAddressApi } from '/@/api-services/api';
import { UniversityAddressEditingInput, UniversityOutput } from '/@/api-services/models';

const props = defineProps<{
	title: string;
	universityList: Array<UniversityOutput>;
}>();
const emits = defineEmits(['handleQuery']);
const ruleFormRef = ref();
const state = reactive({
	loading: false,
	isShowDialog: false,
	ruleForm: {} as UniversityAddressEditingInput,
});

// 表单验证规则
const rules = {
	universityId: [{ required: true, message: '请选择所属大学', trigger: 'change' }],
	campus: [{ required: true, message: '校区名称不能为空', trigger: 'blur' }],
	address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
	longitude: [
		{
			validator: (rule: any, value: any, callback: any) => {
				if (value !== undefined && value !== null && (value < -180 || value > 180)) {
					callback(new Error('经度范围应在-180到180之间'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		},
		{required: true, message: '请输入经度', trigger: 'blur'}
	],
	latitude: [
		{
			validator: (rule: any, value: any, callback: any) => {
				if (value !== undefined && value !== null && (value < -90 || value > 90)) {
					callback(new Error('纬度范围应在-90到90之间'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		},
		{required: true, message: '请输入纬度', trigger: 'blur'}
	],
};

// 打开弹窗
const openDialog = (row: any) => {
	ruleFormRef.value?.resetFields();
	state.ruleForm = {
		id: row?.id || null,
		universityId: row?.universityId || undefined,
		campus: row?.campus || '',
		address: row?.address || '',
		longitude: row?.longitude || undefined,
		latitude: row?.latitude || undefined,
	};
	state.isShowDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	closeDialog();
};

// 提交
const submit = async () => {
	ruleFormRef.value?.validate(async (valid: boolean) => {
		if (!valid) return;
		
		state.loading = true;
		try {
			await getAPI(UniversityAddressApi).apiUniversityAddressSubmitPost(state.ruleForm);
			ElMessage.success('操作成功');
			emits('handleQuery');
			closeDialog();
		} catch (error) {
			ElMessage.error('操作失败');
		} finally {
			state.loading = false;
		}
	});
};

// 暴露方法给父组件调用
defineExpose({
	openDialog,
});
</script>

<style scoped>
.w100 {
	width: 100%;
}
</style>