<template>
	<div class="university-address-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="大学">
					<el-select v-model="state.queryParams.universityId" placeholder="请选择大学" clearable style="width: 200px" @change="handleQuery" filterable>
						<el-option v-for="item in state.universityList" :key="item.id" :label="item.name" :value="item.id" />
					</el-select>
				</el-form-item>
				<el-form-item label="校区名称">
					<el-input v-model="state.queryParams.campus" placeholder="校区名称" clearable />
				</el-form-item>
				<el-form-item label="地址">
					<el-input v-model="state.queryParams.address" placeholder="地址" clearable />
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddUniversityAddress"> 新增 </el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="state.universityAddressData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="universityName" label="大学名称" min-width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="campus" label="校区名称" min-width="120" align="center" show-overflow-tooltip />
				<el-table-column prop="address" label="详细地址" min-width="200" align="center" show-overflow-tooltip />
				<el-table-column label="坐标位置" width="150" align="center" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.longitude && scope.row.latitude">
							{{ scope.row.longitude?.toFixed(6) }}, {{ scope.row.latitude?.toFixed(6) }}
						</span>
						<span v-else style="color: #999">-</span>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" width="110" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.createdTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditUniversityAddress(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="delUniversityAddress(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				size="small"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>

		<EditUniversityAddress ref="editUniversityAddressRef" :title="state.editUniversityAddressTitle" @handleQuery="handleQuery" :universityList="state.universityList" />
	</div>
</template>

<script lang="ts" setup name="universityAddress">
import { onMounted, reactive, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditUniversityAddress from './component/editUniversityAddress.vue';
import { getAPI } from '/@/utils/axios-utils';
import { UniversityAddressApi, UniversityApi } from '/@/api-services/api';
import { UniversityAddressOutput, UniversityOutput } from '/@/api-services/models';
import { formatDate } from '/@/utils/formatTime';

const editUniversityAddressRef = ref<InstanceType<typeof EditUniversityAddress>>();
const state = reactive({
	loading: false,
	universityAddressData: [] as Array<UniversityAddressOutput>,
	universityList: [] as Array<UniversityOutput>,
	queryParams: {
		universityId: undefined,
		campus: undefined,
		address: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 50,
		total: 0 as any,
	},
	editUniversityAddressTitle: '',
});

onMounted(async () => {
	await loadUniversityList();
	await handleQuery();
});

// 加载大学列表
const loadUniversityList = async () => {
	try {
		const res = await getAPI(UniversityApi).apiUniversityPageGet(undefined, undefined, undefined, undefined, 1, 1000);
		state.universityList = res.data.result?.items ?? [];
	} catch (error) {
		ElMessage.error('加载大学列表失败');
	}
};

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign({}, state.queryParams, state.tableParams);
		let res = await getAPI(UniversityAddressApi).apiUniversityAddressPageGet(
			params.universityId,
			params.campus,
			params.address,
			params.page,
			params.pageSize
		);
		state.universityAddressData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
		ElMessage.error('查询失败');
	} finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.universityId = undefined;
	state.queryParams.campus = undefined;
	state.queryParams.address = undefined;
	await handleQuery();
};

// 打开新增页面
const openAddUniversityAddress = () => {
	state.editUniversityAddressTitle = '添加大学地址';
	editUniversityAddressRef.value?.openDialog({});
};

// 打开编辑页面
const openEditUniversityAddress = async (row: any) => {
	state.editUniversityAddressTitle = '编辑大学地址';
	try {
		const res = await getAPI(UniversityAddressApi).apiUniversityAddressDetailIdGet(row.id);
		editUniversityAddressRef.value?.openDialog(res.data.result);
	} catch (error) {
		ElMessage.error('获取大学地址详情失败');
	}
};

// 删除
const delUniversityAddress = (row: any) => {
	ElMessageBox.confirm(`确定删除校区：【${row.campus}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(UniversityAddressApi).apiUniversityAddressDeleteIdDelete(row.id);
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = async (val: number) => {
	state.tableParams.pageSize = val;
	await handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 格式化日期
const formatDateTime = (date: any) => {
	if (!date) return '-';
	return formatDate(new Date(date), 'YYYY-mm-dd');
};
</script>