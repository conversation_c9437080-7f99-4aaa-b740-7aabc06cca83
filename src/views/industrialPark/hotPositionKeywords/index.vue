<template>
	<div class="hot-position-keywords-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-tabs v-model="state.activePlatform" @tab-change="handlePlatformChange" class="platform-tabs">
				<el-tab-pane label="小程序" :name="HotPositionKeywordsPlatform.NUMBER_0" />
				<el-tab-pane label="大屏" :name="HotPositionKeywordsPlatform.NUMBER_1" />
			</el-tabs>
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="产业园">
					<el-select v-model="state.queryParams.industrialParkId" placeholder="请选择产业园" clearable style="width: 200px" @change="handleQuery" filterable>
						<el-option v-for="item in state.industrialParkList" :key="item.id" :label="item.name" :value="item.id" />
					</el-select>
				</el-form-item>
				<el-form-item label="关键词">
					<el-input v-model="state.queryParams.keywords" placeholder="关键词" clearable />
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddHotPositionKeywords"> 新增 </el-button>
				</el-form-item>
				<el-form-item>
					<el-button type="danger" icon="ele-Delete" @click="batchDelete" :disabled="state.selectedIds.length === 0"> 批量删除 </el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table 
				:data="state.hotPositionKeywordsData" 
				style="width: 100%" 
				v-loading="state.loading" 
				border
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55" align="center" fixed />
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="industrialParkName" label="产业园名称" min-width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="keywords" label="关键词" min-width="120" align="center" show-overflow-tooltip />
				<el-table-column label="平台" width="80" align="center">
					<template #default="scope">
						<el-tag :type="scope.row.platform === HotPositionKeywordsPlatform.NUMBER_0 ? 'primary' : 'success'">
							{{ scope.row.platform === HotPositionKeywordsPlatform.NUMBER_0 ? '小程序' : '大屏' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="图片预览" width="120" align="center">
					<template #default="scope">
						<el-image 
							v-if="scope.row.imageUrl" 
							:src="scope.row.imageUrl" 
							fit="cover" 
							style="width: 80px; height: 60px; border-radius: 4px" 
							:preview-src-list="[scope.row.imageUrl]" 
							preview-teleported
						>
							<template #error>
								<div class="image-slot">
									<el-icon><ele-Picture /></el-icon>
								</div>
							</template>
						</el-image>
						<span v-else style="color: #999">-</span>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" width="110" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDateTime(scope.row.createdTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="160" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditHotPositionKeywords(scope.row)"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="danger" @click="delHotPositionKeywords(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				size="small"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>

		<EditHotPositionKeywords ref="editHotPositionKeywordsRef" :title="state.editHotPositionKeywordsTitle" @handleQuery="handleQuery" :industrialParkList="state.industrialParkList" :currentPlatform="state.activePlatform" />
	</div>
</template>

<script lang="ts" setup name="hotPositionKeywords">
import { onMounted, reactive, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditHotPositionKeywords from './component/editHotPositionKeywords.vue';
import { getAPI } from '/@/utils/axios-utils';
import { HotPositionKeywordsApi, UniversityApi } from '/@/api-services/api';
import { HotPositionKeywordsOutput, IndustrialParkBaseInfoOutput, HotPositionKeywordsPlatform } from '/@/api-services/models';
import { formatDate } from '/@/utils/formatTime';

const editHotPositionKeywordsRef = ref<InstanceType<typeof EditHotPositionKeywords>>();
const state = reactive({
	loading: false,
	hotPositionKeywordsData: [] as Array<HotPositionKeywordsOutput>,
	industrialParkList: [] as Array<IndustrialParkBaseInfoOutput>,
	selectedIds: [] as Array<number>,
	activePlatform: HotPositionKeywordsPlatform.NUMBER_0,
	queryParams: {
		industrialParkId: undefined,
		keywords: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 50,
		total: 0 as any,
	},
	editHotPositionKeywordsTitle: '',
});

onMounted(async () => {
	await loadIndustrialParkList();
	await handleQuery();
});

// 加载产业园列表
const loadIndustrialParkList = async () => {
	try {
		// 先获取现有数据中的产业园信息
		const res = await getAPI(UniversityApi).apiUniversityParkListGet();
		
		const items = res.data.result ?? [];
		
		state.industrialParkList = items;
	} catch (error) {
		console.error('加载产业园列表失败:', error);
	}
};

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign({}, state.queryParams, state.tableParams);
		let res = await getAPI(HotPositionKeywordsApi).apiHotPositionKeywordsPageGet(
			params.industrialParkId,
			params.keywords,
			undefined, // imageUrl
			state.activePlatform, // platform
			params.page,
			params.pageSize
		);
		state.hotPositionKeywordsData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
		ElMessage.error('查询失败');
	} finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.industrialParkId = undefined;
	state.queryParams.keywords = undefined;
	await handleQuery();
};

// 处理平台切换
const handlePlatformChange = async (platform: HotPositionKeywordsPlatform) => {
	state.activePlatform = platform;
	state.tableParams.page = 1;
	await handleQuery();
};

// 打开新增页面
const openAddHotPositionKeywords = () => {
	state.editHotPositionKeywordsTitle = '添加热门职位关键词';
	editHotPositionKeywordsRef.value?.openDialog({ platform: state.activePlatform });
};

// 打开编辑页面
const openEditHotPositionKeywords = async (row: any) => {
	state.editHotPositionKeywordsTitle = '编辑热门职位关键词';
	editHotPositionKeywordsRef.value?.openDialog(row);
};

// 删除
const delHotPositionKeywords = (row: any) => {
	ElMessageBox.confirm(`确定删除关键词：【${row.keywords}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(HotPositionKeywordsApi).apiHotPositionKeywordsDeleteIdDelete(row.id);
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 批量删除
const batchDelete = () => {
	if (state.selectedIds.length === 0) {
		ElMessage.warning('请选择要删除的数据');
		return;
	}
	
	ElMessageBox.confirm(`确定删除选中的 ${state.selectedIds.length} 条记录?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				await getAPI(HotPositionKeywordsApi).apiHotPositionKeywordsBatchDeleteDelete(state.selectedIds);
				await handleQuery();
				state.selectedIds = [];
				ElMessage.success('批量删除成功');
			} catch (error) {
				ElMessage.error('批量删除失败');
			}
		})
		.catch(() => {});
};

// 处理表格选择变化
const handleSelectionChange = (selection: HotPositionKeywordsOutput[]) => {
	state.selectedIds = selection.map(item => item.id!).filter(id => id !== undefined);
};

// 改变页面容量
const handleSizeChange = async (val: number) => {
	state.tableParams.pageSize = val;
	await handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 格式化日期
const formatDateTime = (date: any) => {
	if (!date) return '-';
	return formatDate(new Date(date), 'YYYY-mm-dd');
};
</script>

<style scoped lang="scss">
.hot-position-keywords-container {
	.platform-tabs {
		margin-bottom: 16px;
		
		:deep(.el-tabs__header) {
			margin: 0 0 16px 0;
		}
	}
	
	.image-slot {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 80px;
		height: 60px;
		background: var(--el-fill-color-light);
		color: var(--el-text-color-secondary);
		font-size: 24px;
		border-radius: 4px;
	}
}
</style> 