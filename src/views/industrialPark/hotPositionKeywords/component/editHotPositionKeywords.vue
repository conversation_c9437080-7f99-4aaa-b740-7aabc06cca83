<template>
	<div class="edit-hot-position-keywords-container">
		<el-dialog v-model="state.isShowDialog" draggable :close-on-click-modal="false" width="60%">
			<template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="120px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="所属产业园" prop="industrialParkId">
							<el-select v-model="state.ruleForm.industrialParkId" placeholder="请选择产业园" clearable style="width: 100%" filterable>
								<el-option v-for="item in props.industrialParkList" :key="item.id" :label="item.name" :value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="关键词" prop="keywords">
							<el-input v-model="state.ruleForm.keywords" placeholder="请输入关键词" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="平台" prop="platform">
							<el-select v-model="state.ruleForm.platform" placeholder="请选择平台" style="width: 100%">
								<el-option :label="'小程序'" :value="HotPositionKeywordsPlatform.NUMBER_0" />
								<el-option :label="'大屏'" :value="HotPositionKeywordsPlatform.NUMBER_1" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="上传方式">
							<el-radio-group v-model="state.uploadMode">
								<el-radio :label="'file'">文件上传</el-radio>
								<el-radio :label="'url'">URL链接</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-if="state.uploadMode === 'file'" class="mb20">
						<el-form-item label="图片文件">
							<div class="upload-container">
								<el-upload
									ref="uploadRef"
									class="image-uploader"
									:show-file-list="false"
									:before-upload="beforeUpload"
									:on-change="handleFileChange"
									:auto-upload="false"
									accept="image/*"
									drag
								>
									<div v-if="state.imagePreview" class="image-preview">
										<img :src="state.imagePreview" alt="preview" />
										<div class="image-actions">
											<el-button
												type="danger"
												size="small"
												@click.stop="removeImage"
											>
												<el-icon><ele-Delete /></el-icon>
												删除
											</el-button>
										</div>
									</div>
									<div v-else class="upload-placeholder">
										<el-icon class="upload-icon"><ele-Plus /></el-icon>
										<div class="upload-text">点击或拖拽上传图片</div>
										<div class="upload-tips">支持 JPG、PNG、GIF 格式，文件大小不超过 5MB</div>
									</div>
								</el-upload>
							</div>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-if="state.uploadMode === 'url'" class="mb20">
						<el-form-item label="图片URL">
							<el-input
								v-model="state.ruleForm.imageUrl"
								placeholder="请输入图片URL地址"
								clearable
								@blur="previewUrlImage"
							/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-if="state.uploadMode === 'url' && state.ruleForm.imageUrl" class="mb20">
						<el-form-item label="图片预览">
							<el-image
								:src="state.ruleForm.imageUrl"
								fit="cover"
								style="width: 200px; height: 150px; border-radius: 4px"
								:preview-src-list="[state.ruleForm.imageUrl]"
								preview-teleported
							>
								<template #error>
									<div class="image-error">
										<el-icon><ele-Picture /></el-icon>
										<div>图片加载失败</div>
									</div>
								</template>
							</el-image>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="editHotPositionKeywords">
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getAPI } from '/@/utils/axios-utils';
import { HotPositionKeywordsApi } from '/@/api-services/api';
import { HotPositionKeywordsPlatform } from '/@/api-services/models';

const props = defineProps({
	title: String,
	industrialParkList: {
		type: Array as () => Array<{ id: number; name: string }>,
		default: () => []
	},
	currentPlatform: {
		type: Number as () => HotPositionKeywordsPlatform,
		default: HotPositionKeywordsPlatform.NUMBER_0
	}
});
const emits = defineEmits(['handleQuery']);
const ruleFormRef = ref();
const uploadRef = ref();
const state = reactive({
	loading: false,
	isShowDialog: false,
	uploadMode: 'file' as 'file' | 'url',
	imagePreview: '',
	selectedFile: null as File | null,
	ruleForm: {
		id: undefined as number | undefined,
		industrialParkId: undefined as number | undefined,
		keywords: '',
		imageUrl: '',
		platform: HotPositionKeywordsPlatform.NUMBER_0,
	},
});

// 表单验证规则
const rules = {
	industrialParkId: [{ required: true, message: '请选择产业园', trigger: 'change' }],
	keywords: [{ required: true, message: '关键词不能为空', trigger: 'blur' }],
	platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
};

// 打开弹窗
const openDialog = (row: any) => {
	ruleFormRef.value?.resetFields();
	state.ruleForm = {
		id: row?.id || undefined,
		industrialParkId: row?.industrialParkId || undefined,
		keywords: row?.keywords || '',
		imageUrl: row?.imageUrl || '',
		platform: row?.platform !== undefined ? row.platform : props.currentPlatform,
	};
	
	// 如果有图片URL，设置为URL模式并显示预览
	if (state.ruleForm.imageUrl) {
		state.uploadMode = 'url';
	} else {
		state.uploadMode = 'file';
	}
	
	state.imagePreview = '';
	state.selectedFile = null;
	state.isShowDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	closeDialog();
};

// 文件上传前验证
const beforeUpload = (file: File) => {
	const isImage = file.type.startsWith('image/');
	const isLt5M = file.size / 1024 / 1024 < 5;

	if (!isImage) {
		ElMessage.error('只能上传图片文件!');
		return false;
	}
	if (!isLt5M) {
		ElMessage.error('图片大小不能超过 5MB!');
		return false;
	}
	return false; // 阻止自动上传
};

// 文件选择变化
const handleFileChange = (file: any) => {
	if (file.raw) {
		state.selectedFile = file.raw;
		// 创建预览
		const reader = new FileReader();
		reader.onload = (e) => {
			state.imagePreview = e.target?.result as string;
		};
		reader.readAsDataURL(file.raw);
	}
};

// 删除图片
const removeImage = () => {
	state.imagePreview = '';
	state.selectedFile = null;
	uploadRef.value?.clearFiles();
};

// URL图片预览
const previewUrlImage = () => {
	// URL模式下的预览已通过v-if和el-image实现
};

// 提交
const submit = async () => {
	ruleFormRef.value?.validate(async (valid: boolean) => {
		if (!valid) return;
		
		// 校验图片上传
		if (state.uploadMode === 'file' && !state.selectedFile) {
			ElMessage.error('请选择要上传的图片文件');
			return;
		}
		if (state.uploadMode === 'url' && !state.ruleForm.imageUrl) {
			ElMessage.error('请输入图片URL地址');
			return;
		}
		
		state.loading = true;
		try {
			// 准备FormData
			const formData = new FormData();
			
			if (state.ruleForm.id) {
				formData.append('Id', state.ruleForm.id.toString());
			}
			formData.append('IndustrialParkId', state.ruleForm.industrialParkId!.toString());
			formData.append('Keywords', state.ruleForm.keywords);
			formData.append('Platform', state.ruleForm.platform.toString());
			
			if (state.uploadMode === 'file' && state.selectedFile) {
				formData.append('ImageFile', state.selectedFile);
			} else if (state.uploadMode === 'url' && state.ruleForm.imageUrl) {
				formData.append('ImageUrl', state.ruleForm.imageUrl);
			}
			
			// 调用API
			await getAPI(HotPositionKeywordsApi).apiHotPositionKeywordsSubmitPostForm(
				state.ruleForm.id,
				state.ruleForm.industrialParkId,
				state.ruleForm.keywords,
				state.uploadMode === 'file' ? state.selectedFile || undefined : undefined,
				state.uploadMode === 'url' ? state.ruleForm.imageUrl : undefined,
				state.ruleForm.platform
			);
			
			ElMessage.success('操作成功');
			emits('handleQuery');
			closeDialog();
		} catch (error) {
			ElMessage.error('操作失败');
		} finally {
			state.loading = false;
		}
	});
};

// 暴露方法给父组件调用
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.edit-hot-position-keywords-container {
	.upload-container {
		width: 100%;
		
		.image-uploader {
			width: 100%;
			
			:deep(.el-upload) {
				width: 100%;
				border: 1px dashed var(--el-border-color);
				border-radius: 6px;
				cursor: pointer;
				position: relative;
				overflow: hidden;
				transition: var(--el-transition-duration-fast);
				
				&:hover {
					border-color: var(--el-color-primary);
				}
			}
		}
		
		.image-preview {
			position: relative;
			width: 100%;
			height: 200px;
			
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				border-radius: 6px;
			}
			
			.image-actions {
				position: absolute;
				top: 10px;
				right: 10px;
				display: flex;
				gap: 8px;
			}
		}
		
		.upload-placeholder {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 200px;
			color: var(--el-text-color-secondary);
			
			.upload-icon {
				font-size: 48px;
				margin-bottom: 16px;
			}
			
			.upload-text {
				font-size: 16px;
				margin-bottom: 8px;
			}
			
			.upload-tips {
				font-size: 12px;
				color: var(--el-text-color-placeholder);
			}
		}
	}
	
	.image-error {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 200px;
		height: 150px;
		background: var(--el-fill-color-light);
		color: var(--el-text-color-secondary);
		border-radius: 4px;
		
		.el-icon {
			font-size: 32px;
			margin-bottom: 8px;
		}
	}
}
</style> 