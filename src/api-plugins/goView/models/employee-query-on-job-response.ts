/* tslint:disable */
/* eslint-disable */
/**
 * DingTalk
 * 集成钉钉开放平台<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EmployeeQueryOnJobResponseResultDomain } from './employee-query-on-job-response-result-domain';
/**
 * 
 * @export
 * @interface EmployeeQueryOnJobResponse
 */
export interface EmployeeQueryOnJobResponse {
    /**
     * 返回码描述=ok
     * @type {string}
     * @memberof EmployeeQueryOnJobResponse
     */
    errMsg?: string | null;
    /**
     * 返回码=0
     * @type {number}
     * @memberof EmployeeQueryOnJobResponse
     */
    errCode?: number;
    /**
     * 接口调用是否成功
     * @type {boolean}
     * @memberof EmployeeQueryOnJobResponse
     */
    success?: boolean;
    /**
     * 
     * @type {string}
     * @memberof EmployeeQueryOnJobResponse
     */
    requestId?: string | null;
    /**
     * 
     * @type {EmployeeQueryOnJobResponseResultDomain}
     * @memberof EmployeeQueryOnJobResponse
     */
    result?: EmployeeQueryOnJobResponseResultDomain;
}
