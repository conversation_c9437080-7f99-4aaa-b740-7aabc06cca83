/* tslint:disable */
/* eslint-disable */
/**
 * ApprovalFlow
 * <br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { Filter } from './filter';
import { Search } from './search';
/**
 * 审批流分页查询输入参数
 * @export
 * @interface ApprovalFlowInput
 */
export interface ApprovalFlowInput {
    /**
     * 
     * @type {Search}
     * @memberof ApprovalFlowInput
     */
    search?: Search;
    /**
     * 模糊查询关键字
     * @type {string}
     * @memberof ApprovalFlowInput
     */
    keyword?: string | null;
    /**
     * 
     * @type {Filter}
     * @memberof ApprovalFlowInput
     */
    filter?: Filter;
    /**
     * 当前页码
     * @type {number}
     * @memberof ApprovalFlowInput
     */
    page?: number;
    /**
     * 页码容量
     * @type {number}
     * @memberof ApprovalFlowInput
     */
    pageSize?: number;
    /**
     * 排序字段
     * @type {string}
     * @memberof ApprovalFlowInput
     */
    field?: string | null;
    /**
     * 排序方向
     * @type {string}
     * @memberof ApprovalFlowInput
     */
    order?: string | null;
    /**
     * 降序排序
     * @type {string}
     * @memberof ApprovalFlowInput
     */
    descStr?: string | null;
    /**
     * 编号
     * @type {string}
     * @memberof ApprovalFlowInput
     */
    code?: string | null;
    /**
     * 名称
     * @type {string}
     * @memberof ApprovalFlowInput
     */
    name?: string | null;
    /**
     * 备注
     * @type {string}
     * @memberof ApprovalFlowInput
     */
    remark?: string | null;
}
