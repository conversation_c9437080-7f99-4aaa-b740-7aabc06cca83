/* 自定义页面切换动画
------------------------------- */
.fade-enter-active{
	animation: fadeIn;
	animation-duration: 0.3s;
}
.fadeUp-enter-active{
	animation: fadeInUp;
	animation-duration: 0.3s;
}
.fadeDown-enter-active{
	animation: fadeInDown;
	animation-duration: 0.3s;
}
.fadeLeft-enter-active{
	animation: fadeInLeft;
	animation-duration: 0.3s;
}
.fadeRight-enter-active{
	animation: fadeInRight;
	animation-duration: 0.3s;
}

.lightSpeedLeft-enter-active{
	animation: lightSpeedInLeft;
	animation-duration: 0.3s;
}
.lightSpeedRight-enter-active{
	animation: lightSpeedInRight;
	animation-duration: 0.3s;
}

.zoom-enter-active{
	animation: zoomIn;
	animation-duration: 0.3s;
}
.zoomUp-enter-active{
	animation: zoomInUp;
	animation-duration: 0.3s;
}
.zoomDown-enter-active{
	animation: zoomInDown;
	animation-duration: 0.3s;
}
.zoomLeft-enter-active{
	animation: zoomInLeft;
	animation-duration: 0.3s;
}
.zoomRight-enter-active{
	animation: zoomInRight;
	animation-duration: 0.3s;
}

.flip-enter-active{
	animation: flip;
	animation-duration: 0.3s;
}
.flipX-enter-active{
	animation: flipInX;
	animation-duration: 0.3s;
}
.flipY-enter-active{
	animation: flipInY;
	animation-duration: 0.3s;
}

.backUp-enter-active{
	animation: backInUp;
	animation-duration: 0.3s;
}
.backDown-enter-active{
	animation: backInDown;
	animation-duration: 0.3s;
}
.backLeft-enter-active{
	animation: backInLeft;
	animation-duration: 0.3s;
}
.backRight-enter-active{
	animation: backInRight;
	animation-duration: 0.3s;
}

.bounce-enter-active{
	animation: bounceIn;
	animation-duration: 0.3s;
}
.bounceUp-enter-active{
	animation: bounceInUp;
	animation-duration: 0.3s;
}
.bounceDown-enter-active{
	animation: bounceInDown;
	animation-duration: 0.3s;
}
.bounceLeft-enter-active{
	animation: bounceInLeft;
	animation-duration: 0.3s;
}
.bounceRight-enter-active{
	animation: bounceInRight;
	animation-duration: 0.3s;
}

// // 自定义弹窗动画
// .dialog-fade-enter-active .el-dialog{
// 	animation: bounceInDown;
// 	animation-duration: 0.3s;
// }
// .dialog-fade-leave-active .el-dialog{
// 	animation: bounceOutRight;
// 	animation-duration: 0.3s;
// }


/* 页面切换动画
------------------------------- */
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
	will-change: transform;
	transition: all 0.3s ease;
}
// slide-right
.slide-right-enter-from {
	opacity: 0;
	transform: translateX(-20px);
}
.slide-right-leave-to {
	opacity: 0;
	transform: translateX(20px);
}
// slide-left
.slide-left-enter-from {
	@extend .slide-right-leave-to;
}
.slide-left-leave-to {
	@extend .slide-right-enter-from;
}
// opacitys
.opacitys-enter-active,
.opacitys-leave-active {
	will-change: transform;
	transition: all 0.3s ease;
}
.opacitys-enter-from,
.opacitys-leave-to {
	opacity: 0;
}

/* Breadcrumb 面包屑过渡动画
------------------------------- */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
	transition: all 0.5s ease;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
	opacity: 0;
	transform: translateX(20px);
}
.breadcrumb-leave-active {
	position: absolute;
	z-index: -1;
}

/* logo 过渡动画
------------------------------- */
@keyframes logoAnimation {
	0% {
		transform: scale(0);
	}
	80% {
		transform: scale(1.2);
	}
	100% {
		transform: scale(1);
	}
}

/* 404、401 过渡动画
------------------------------- */
@keyframes error-num {
	0% {
		transform: translateY(60px);
		opacity: 0;
	}
	100% {
		transform: translateY(0);
		opacity: 1;
	}
}
@keyframes error-img {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
@keyframes error-img-two {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}

/* 登录页动画
------------------------------- */
@keyframes loginLeft {
	0% {
		left: -100%;
	}
	50%,
	100% {
		left: 100%;
	}
}
@keyframes loginTop {
	0% {
		top: -100%;
	}
	50%,
	100% {
		top: 100%;
	}
}
@keyframes loginRight {
	0% {
		right: -100%;
	}
	50%,
	100% {
		right: 100%;
	}
}
@keyframes loginBottom {
	0% {
		bottom: -100%;
	}
	50%,
	100% {
		bottom: 100%;
	}
}

/* 左右左 link.vue
------------------------------- */
@keyframes toRight {
	0% {
		left: -5px;
	}
	50% {
		left: 100%;
	}
	100% {
		left: -5px;
	}
}
