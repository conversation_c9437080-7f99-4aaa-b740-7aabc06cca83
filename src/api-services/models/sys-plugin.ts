/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { StatusEnum } from './status-enum';
/**
 * 系统动态插件表
 * @export
 * @interface SysPlugin
 */
export interface SysPlugin {
    /**
     * 雪花Id
     * @type {number}
     * @memberof SysPlugin
     */
    id?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof SysPlugin
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof SysPlugin
     */
    updateTime?: Date | null;
    /**
     * 创建者Id
     * @type {number}
     * @memberof SysPlugin
     */
    createUserId?: number | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof SysPlugin
     */
    createUserName?: string | null;
    /**
     * 修改者Id
     * @type {number}
     * @memberof SysPlugin
     */
    updateUserId?: number | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof SysPlugin
     */
    updateUserName?: string | null;
    /**
     * 软删除
     * @type {boolean}
     * @memberof SysPlugin
     */
    isDelete?: boolean;
    /**
     * 租户Id
     * @type {number}
     * @memberof SysPlugin
     */
    tenantId?: number | null;
    /**
     * 名称
     * @type {string}
     * @memberof SysPlugin
     */
    name: string;
    /**
     * C#代码
     * @type {string}
     * @memberof SysPlugin
     */
    csharpCode: string;
    /**
     * 程序集名称
     * @type {string}
     * @memberof SysPlugin
     */
    assemblyName?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof SysPlugin
     */
    orderNo?: number;
    /**
     * 
     * @type {StatusEnum}
     * @memberof SysPlugin
     */
    status?: StatusEnum;
    /**
     * 备注
     * @type {string}
     * @memberof SysPlugin
     */
    remark?: string | null;
}
