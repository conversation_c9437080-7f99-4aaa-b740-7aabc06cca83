/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PropagandaPictureLocation } from './propaganda-picture-location';
/**
 * 宣传图片输出参数
 * @export
 * @interface PropagandaPictureOutput
 */
export interface PropagandaPictureOutput {
    /**
     * 主键
     * @type {number}
     * @memberof PropagandaPictureOutput
     */
    id?: number;
    /**
     * 唯一标识
     * @type {string}
     * @memberof PropagandaPictureOutput
     */
    guid?: string;
    /**
     * 标题
     * @type {string}
     * @memberof PropagandaPictureOutput
     */
    title?: string | null;
    /**
     * 
     * @type {PropagandaPictureLocation}
     * @memberof PropagandaPictureOutput
     */
    location?: PropagandaPictureLocation;
    /**
     * 上线开始时间
     * @type {Date}
     * @memberof PropagandaPictureOutput
     */
    onlineStartTime?: Date;
    /**
     * 上线结束时间
     * @type {Date}
     * @memberof PropagandaPictureOutput
     */
    onlineEndTime?: Date;
    /**
     * 图片URL
     * @type {string}
     * @memberof PropagandaPictureOutput
     */
    url?: string | null;
    /**
     * 跳转链接
     * @type {string}
     * @memberof PropagandaPictureOutput
     */
    link?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof PropagandaPictureOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof PropagandaPictureOutput
     */
    updateTime?: Date | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof PropagandaPictureOutput
     */
    createUserName?: string | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof PropagandaPictureOutput
     */
    updateUserName?: string | null;
    /**
     * 软删除
     * @type {boolean}
     * @memberof PropagandaPictureOutput
     */
    isDelete?: boolean;
}
