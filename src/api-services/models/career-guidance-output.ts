/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { CareerGuidanceVideoOutput } from './career-guidance-video-output';
/**
 * 职业指导输出参数
 * @export
 * @interface CareerGuidanceOutput
 */
export interface CareerGuidanceOutput {
    /**
     * 主键Id
     * @type {number}
     * @memberof CareerGuidanceOutput
     */
    id?: number;
    /**
     * 唯一标识
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    guid?: string;
    /**
     * 类型（1：就业指导 2：求职指导 3：职场能力）
     * @type {number}
     * @memberof CareerGuidanceOutput
     */
    type?: number;
    /**
     * 就业指导标题
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    title?: string | null;
    /**
     * 课时
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    classHours?: string | null;
    /**
     * 讲师
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    lecturer?: string | null;
    /**
     * 图片
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    image?: string | null;
    /**
     * 视频列表
     * @type {Array<CareerGuidanceVideoOutput>}
     * @memberof CareerGuidanceOutput
     */
    videos?: Array<CareerGuidanceVideoOutput> | null;
    /**
     * 课程简介
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    shortCourseIntroduction?: string | null;
    /**
     * 课程介绍
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    courseIntroduction?: string | null;
    /**
     * 是否发布线上
     * @type {boolean}
     * @memberof CareerGuidanceOutput
     */
    isPublished?: boolean;
    /**
     * 发布时间
     * @type {Date}
     * @memberof CareerGuidanceOutput
     */
    publishTime?: Date;
    /**
     * 创建时间
     * @type {Date}
     * @memberof CareerGuidanceOutput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof CareerGuidanceOutput
     */
    updateTime?: Date | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    createUserName?: string | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof CareerGuidanceOutput
     */
    updateUserName?: string | null;
    /**
     * 软删除
     * @type {boolean}
     * @memberof CareerGuidanceOutput
     */
    isDelete?: boolean;
}
