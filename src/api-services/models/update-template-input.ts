/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { TemplateTypeEnum } from './template-type-enum';
/**
 * 更新模板输入参数
 * @export
 * @interface UpdateTemplateInput
 */
export interface UpdateTemplateInput {
    /**
     * 创建时间
     * @type {Date}
     * @memberof UpdateTemplateInput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof UpdateTemplateInput
     */
    updateTime?: Date | null;
    /**
     * 创建者Id
     * @type {number}
     * @memberof UpdateTemplateInput
     */
    createUserId?: number | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof UpdateTemplateInput
     */
    createUserName?: string | null;
    /**
     * 修改者Id
     * @type {number}
     * @memberof UpdateTemplateInput
     */
    updateUserId?: number | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof UpdateTemplateInput
     */
    updateUserName?: string | null;
    /**
     * 软删除
     * @type {boolean}
     * @memberof UpdateTemplateInput
     */
    isDelete?: boolean;
    /**
     * 租户Id
     * @type {number}
     * @memberof UpdateTemplateInput
     */
    tenantId?: number | null;
    /**
     * 备注
     * @type {string}
     * @memberof UpdateTemplateInput
     */
    remark?: string | null;
    /**
     * 排序
     * @type {number}
     * @memberof UpdateTemplateInput
     */
    orderNo?: number;
    /**
     * 名称
     * @type {string}
     * @memberof UpdateTemplateInput
     */
    name: string;
    /**
     * 
     * @type {TemplateTypeEnum}
     * @memberof UpdateTemplateInput
     */
    type?: TemplateTypeEnum;
    /**
     * 编码
     * @type {string}
     * @memberof UpdateTemplateInput
     */
    code: string;
    /**
     * 分组名称
     * @type {string}
     * @memberof UpdateTemplateInput
     */
    groupName: string;
    /**
     * 模板内容
     * @type {string}
     * @memberof UpdateTemplateInput
     */
    content: string;
    /**
     * 主键Id
     * @type {number}
     * @memberof UpdateTemplateInput
     */
    id: number;
}
