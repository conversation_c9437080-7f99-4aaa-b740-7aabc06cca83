/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { CareerGuidanceVideoEditingInput } from './career-guidance-video-editing-input';
/**
 * 
 * @export
 * @interface CareerGuidanceSaveBody
 */
export interface CareerGuidanceSaveBody {
    /**
     * 主键Id（更新时必填）
     * @type {number}
     * @memberof CareerGuidanceSaveBody
     */
    id?: number;
    /**
     * 类型（1：就业指导 2：求职指导 3：职场能力）
     * @type {number}
     * @memberof CareerGuidanceSaveBody
     */
    type?: number;
    /**
     * 就业指导标题
     * @type {string}
     * @memberof CareerGuidanceSaveBody
     */
    title: string;
    /**
     * 课时
     * @type {string}
     * @memberof CareerGuidanceSaveBody
     */
    classHours?: string;
    /**
     * 讲师
     * @type {string}
     * @memberof CareerGuidanceSaveBody
     */
    lecturer?: string;
    /**
     * 图片URL相对路径（上传文件时可为空）
     * @type {string}
     * @memberof CareerGuidanceSaveBody
     */
    image?: string;
    /**
     * 上传的图片文件
     * @type {Blob}
     * @memberof CareerGuidanceSaveBody
     */
    imageFile?: Blob;
    /**
     * 视频列表
     * @type {Array<CareerGuidanceVideoEditingInput>}
     * @memberof CareerGuidanceSaveBody
     */
    videos?: Array<CareerGuidanceVideoEditingInput>;
    /**
     * 课程简介
     * @type {string}
     * @memberof CareerGuidanceSaveBody
     */
    shortCourseIntroduction?: string;
    /**
     * 课程介绍
     * @type {string}
     * @memberof CareerGuidanceSaveBody
     */
    courseIntroduction?: string;
    /**
     * 是否发布线上
     * @type {boolean}
     * @memberof CareerGuidanceSaveBody
     */
    isPublished?: boolean;
    /**
     * 发布时间
     * @type {Date}
     * @memberof CareerGuidanceSaveBody
     */
    publishTime?: Date;
}
