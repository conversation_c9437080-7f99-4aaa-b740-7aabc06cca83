/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * ��Ƹֱ�����
 * @export
 * @interface RecruitmentLiveOutput
 */
export interface RecruitmentLiveOutput {
    /**
     * ����
     * @type {number}
     * @memberof RecruitmentLiveOutput
     */
    id?: number;
    /**
     * Ψһ��ʶ
     * @type {string}
     * @memberof RecruitmentLiveOutput
     */
    guid?: string;
    /**
     * ����
     * @type {string}
     * @memberof RecruitmentLiveOutput
     */
    title?: string | null;
    /**
     * ��ʼʱ��
     * @type {Date}
     * @memberof RecruitmentLiveOutput
     */
    startTime?: Date;
    /**
     * ����ʱ��
     * @type {Date}
     * @memberof RecruitmentLiveOutput
     */
    endTime?: Date;
    /**
     * ����ͼƬURL
     * @type {string}
     * @memberof RecruitmentLiveOutput
     */
    coverImage?: string | null;
    /**
     * ���
     * @type {string}
     * @memberof RecruitmentLiveOutput
     */
    briefIntroduction?: string | null;
    /**
     * ��ҵ�б�
     * @type {string}
     * @memberof RecruitmentLiveOutput
     */
    enterprises?: string | null;
    /**
     * ����ʱ��
     * @type {Date}
     * @memberof RecruitmentLiveOutput
     */
    createTime?: Date | null;
    /**
     * ����ʱ��
     * @type {Date}
     * @memberof RecruitmentLiveOutput
     */
    updateTime?: Date | null;
    /**
     * ����������
     * @type {string}
     * @memberof RecruitmentLiveOutput
     */
    createUserName?: string | null;
    /**
     * �޸�������
     * @type {string}
     * @memberof RecruitmentLiveOutput
     */
    updateUserName?: string | null;
    /**
     * ��ɾ��
     * @type {boolean}
     * @memberof RecruitmentLiveOutput
     */
    isDelete?: boolean;
}
