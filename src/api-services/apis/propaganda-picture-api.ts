/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultPropagandaPictureOutput } from '../models';
import { AdminResultSqlSugarPagedListPropagandaPictureOutput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
/**
 * PropagandaPictureApi - axios parameter creator
 * @export
 */
export const PropagandaPictureApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除宣传图片
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPropagandaPictureDeleteIdDelete: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiPropagandaPictureDeleteIdDelete.');
            }
            const localVarPath = `/api/propagandaPicture/delete/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取宣传图片详情
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPropagandaPictureDetailIdGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiPropagandaPictureDetailIdGet.');
            }
            const localVarPath = `/api/propagandaPicture/detail/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取宣传图片分页列表
         * @param {string} [title] 
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPropagandaPicturePageGet: async (title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/propagandaPicture/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (title !== undefined) {
                localVarQueryParameter['Title'] = title;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 添加或更新宣传图片
         * @param {number} [id] 
         * @param {string} [title] 
         * @param {number} [location] 
         * @param {Date} [onlineStartTime] 
         * @param {Date} [onlineEndTime] 
         * @param {string} [urlPath] 
         * @param {Blob} [urlPathFile] 
         * @param {string} [link] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPropagandaPictureSubmitPostForm: async (id?: number, title?: string, location?: number, onlineStartTime?: Date, onlineEndTime?: Date, urlPath?: string, urlPathFile?: Blob, link?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/propagandaPicture/submit`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required


            if (id !== undefined) { 
                localVarFormParams.append('Id', id as any);
            }

            if (title !== undefined) { 
                localVarFormParams.append('Title', title as any);
            }

            if (location !== undefined) { 
                localVarFormParams.append('Location', location as any);
            }

            if (onlineStartTime !== undefined) { 
                localVarFormParams.append('OnlineStartTime', onlineStartTime as any);
            }

            if (onlineEndTime !== undefined) { 
                localVarFormParams.append('OnlineEndTime', onlineEndTime as any);
            }

            if (urlPath !== undefined) { 
                localVarFormParams.append('UrlPath', urlPath as any);
            }

            if (urlPathFile !== undefined) { 
                localVarFormParams.append('UrlPathFile', urlPathFile as any);
            }

            if (link !== undefined) { 
                localVarFormParams.append('Link', link as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PropagandaPictureApi - functional programming interface
 * @export
 */
export const PropagandaPictureApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除宣传图片
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPictureDeleteIdDelete(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PropagandaPictureApiAxiosParamCreator(configuration).apiPropagandaPictureDeleteIdDelete(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取宣传图片详情
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPictureDetailIdGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultPropagandaPictureOutput>>> {
            const localVarAxiosArgs = await PropagandaPictureApiAxiosParamCreator(configuration).apiPropagandaPictureDetailIdGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取宣传图片分页列表
         * @param {string} [title] 
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPicturePageGet(title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListPropagandaPictureOutput>>> {
            const localVarAxiosArgs = await PropagandaPictureApiAxiosParamCreator(configuration).apiPropagandaPicturePageGet(title, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 添加或更新宣传图片
         * @param {number} [id] 
         * @param {string} [title] 
         * @param {number} [location] 
         * @param {Date} [onlineStartTime] 
         * @param {Date} [onlineEndTime] 
         * @param {string} [urlPath] 
         * @param {Blob} [urlPathFile] 
         * @param {string} [link] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPictureSubmitPostForm(id?: number, title?: string, location?: number, onlineStartTime?: Date, onlineEndTime?: Date, urlPath?: string, urlPathFile?: Blob, link?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PropagandaPictureApiAxiosParamCreator(configuration).apiPropagandaPictureSubmitPostForm(id, title, location, onlineStartTime, onlineEndTime, urlPath, urlPathFile, link, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * PropagandaPictureApi - factory interface
 * @export
 */
export const PropagandaPictureApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 删除宣传图片
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPictureDeleteIdDelete(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PropagandaPictureApiFp(configuration).apiPropagandaPictureDeleteIdDelete(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取宣传图片详情
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPictureDetailIdGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultPropagandaPictureOutput>> {
            return PropagandaPictureApiFp(configuration).apiPropagandaPictureDetailIdGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取宣传图片分页列表
         * @param {string} [title] 
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPicturePageGet(title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListPropagandaPictureOutput>> {
            return PropagandaPictureApiFp(configuration).apiPropagandaPicturePageGet(title, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 添加或更新宣传图片
         * @param {number} [id] 
         * @param {string} [title] 
         * @param {number} [location] 
         * @param {Date} [onlineStartTime] 
         * @param {Date} [onlineEndTime] 
         * @param {string} [urlPath] 
         * @param {Blob} [urlPathFile] 
         * @param {string} [link] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPropagandaPictureSubmitPostForm(id?: number, title?: string, location?: number, onlineStartTime?: Date, onlineEndTime?: Date, urlPath?: string, urlPathFile?: Blob, link?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PropagandaPictureApiFp(configuration).apiPropagandaPictureSubmitPostForm(id, title, location, onlineStartTime, onlineEndTime, urlPath, urlPathFile, link, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PropagandaPictureApi - object-oriented interface
 * @export
 * @class PropagandaPictureApi
 * @extends {BaseAPI}
 */
export class PropagandaPictureApi extends BaseAPI {
    /**
     * 
     * @summary 删除宣传图片
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PropagandaPictureApi
     */
    public async apiPropagandaPictureDeleteIdDelete(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PropagandaPictureApiFp(this.configuration).apiPropagandaPictureDeleteIdDelete(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取宣传图片详情
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PropagandaPictureApi
     */
    public async apiPropagandaPictureDetailIdGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultPropagandaPictureOutput>> {
        return PropagandaPictureApiFp(this.configuration).apiPropagandaPictureDetailIdGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取宣传图片分页列表
     * @param {string} [title] 
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PropagandaPictureApi
     */
    public async apiPropagandaPicturePageGet(title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListPropagandaPictureOutput>> {
        return PropagandaPictureApiFp(this.configuration).apiPropagandaPicturePageGet(title, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 添加或更新宣传图片
     * @param {number} [id] 
     * @param {string} [title] 
     * @param {number} [location] 
     * @param {Date} [onlineStartTime] 
     * @param {Date} [onlineEndTime] 
     * @param {string} [urlPath] 
     * @param {Blob} [urlPathFile] 
     * @param {string} [link] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PropagandaPictureApi
     */
    public async apiPropagandaPictureSubmitPostForm(id?: number, title?: string, location?: number, onlineStartTime?: Date, onlineEndTime?: Date, urlPath?: string, urlPathFile?: Blob, link?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PropagandaPictureApiFp(this.configuration).apiPropagandaPictureSubmitPostForm(id, title, location, onlineStartTime, onlineEndTime, urlPath, urlPathFile, link, options).then((request) => request(this.axios, this.basePath));
    }
}
