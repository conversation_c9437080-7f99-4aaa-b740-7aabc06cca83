/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultRecruitmentLiveOutput } from '../models';
import { AdminResultSqlSugarPagedListRecruitmentLiveOutput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
/**
 * RecruitmentLiveApi - axios parameter creator
 * @export
 */
export const RecruitmentLiveApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除招聘直播 🔖
         * @param {number} id 直播ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRecruitmentLiveDeleteIdDelete: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiRecruitmentLiveDeleteIdDelete.');
            }
            const localVarPath = `/api/recruitmentLive/delete/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取招聘直播详情 🔖
         * @param {number} id 直播ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRecruitmentLiveDetailIdGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiRecruitmentLiveDetailIdGet.');
            }
            const localVarPath = `/api/recruitmentLive/detail/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取招聘直播分页列表 🔖
         * @param {string} [title] ����ؼ���
         * @param {Date} [startTimeBegin] ��ʼʱ�䷶Χ���
         * @param {Date} [startTimeEnd] ��ʼʱ�䷶Χ�յ�
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRecruitmentLivePageGet: async (title?: string, startTimeBegin?: Date, startTimeEnd?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/recruitmentLive/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (title !== undefined) {
                localVarQueryParameter['Title'] = title;
            }

            if (startTimeBegin !== undefined) {
                localVarQueryParameter['StartTimeBegin'] = (startTimeBegin as any instanceof Date) ?
                    (startTimeBegin as any).toISOString() :
                    startTimeBegin;
            }

            if (startTimeEnd !== undefined) {
                localVarQueryParameter['StartTimeEnd'] = (startTimeEnd as any instanceof Date) ?
                    (startTimeEnd as any).toISOString() :
                    startTimeEnd;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 添加或更新招聘直播 🔖
         * @param {number} [id] 
         * @param {string} [title] 
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {Blob} [coverImageFile] 
         * @param {string} [coverImage] 
         * @param {string} [briefIntroduction] 
         * @param {string} [enterprises] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRecruitmentLiveSubmitPostForm: async (id?: number, title?: string, startTime?: Date, endTime?: Date, coverImageFile?: Blob, coverImage?: string, briefIntroduction?: string, enterprises?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/recruitmentLive/submit`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required


            if (id !== undefined) { 
                localVarFormParams.append('Id', id as any);
            }

            if (title !== undefined) { 
                localVarFormParams.append('Title', title as any);
            }

            if (startTime !== undefined) { 
                localVarFormParams.append('StartTime', startTime as any);
            }

            if (endTime !== undefined) { 
                localVarFormParams.append('EndTime', endTime as any);
            }

            if (coverImageFile !== undefined) { 
                localVarFormParams.append('CoverImageFile', coverImageFile as any);
            }

            if (coverImage !== undefined) { 
                localVarFormParams.append('CoverImage', coverImage as any);
            }

            if (briefIntroduction !== undefined) { 
                localVarFormParams.append('BriefIntroduction', briefIntroduction as any);
            }

            if (enterprises !== undefined) { 
                localVarFormParams.append('Enterprises', enterprises as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * RecruitmentLiveApi - functional programming interface
 * @export
 */
export const RecruitmentLiveApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除招聘直播 🔖
         * @param {number} id 直播ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLiveDeleteIdDelete(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await RecruitmentLiveApiAxiosParamCreator(configuration).apiRecruitmentLiveDeleteIdDelete(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取招聘直播详情 🔖
         * @param {number} id 直播ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLiveDetailIdGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultRecruitmentLiveOutput>>> {
            const localVarAxiosArgs = await RecruitmentLiveApiAxiosParamCreator(configuration).apiRecruitmentLiveDetailIdGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取招聘直播分页列表 🔖
         * @param {string} [title] ����ؼ���
         * @param {Date} [startTimeBegin] ��ʼʱ�䷶Χ���
         * @param {Date} [startTimeEnd] ��ʼʱ�䷶Χ�յ�
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLivePageGet(title?: string, startTimeBegin?: Date, startTimeEnd?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListRecruitmentLiveOutput>>> {
            const localVarAxiosArgs = await RecruitmentLiveApiAxiosParamCreator(configuration).apiRecruitmentLivePageGet(title, startTimeBegin, startTimeEnd, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 添加或更新招聘直播 🔖
         * @param {number} [id] 
         * @param {string} [title] 
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {Blob} [coverImageFile] 
         * @param {string} [coverImage] 
         * @param {string} [briefIntroduction] 
         * @param {string} [enterprises] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLiveSubmitPostForm(id?: number, title?: string, startTime?: Date, endTime?: Date, coverImageFile?: Blob, coverImage?: string, briefIntroduction?: string, enterprises?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await RecruitmentLiveApiAxiosParamCreator(configuration).apiRecruitmentLiveSubmitPostForm(id, title, startTime, endTime, coverImageFile, coverImage, briefIntroduction, enterprises, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * RecruitmentLiveApi - factory interface
 * @export
 */
export const RecruitmentLiveApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 删除招聘直播 🔖
         * @param {number} id 直播ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLiveDeleteIdDelete(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return RecruitmentLiveApiFp(configuration).apiRecruitmentLiveDeleteIdDelete(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取招聘直播详情 🔖
         * @param {number} id 直播ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLiveDetailIdGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultRecruitmentLiveOutput>> {
            return RecruitmentLiveApiFp(configuration).apiRecruitmentLiveDetailIdGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取招聘直播分页列表 🔖
         * @param {string} [title] ����ؼ���
         * @param {Date} [startTimeBegin] ��ʼʱ�䷶Χ���
         * @param {Date} [startTimeEnd] ��ʼʱ�䷶Χ�յ�
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLivePageGet(title?: string, startTimeBegin?: Date, startTimeEnd?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListRecruitmentLiveOutput>> {
            return RecruitmentLiveApiFp(configuration).apiRecruitmentLivePageGet(title, startTimeBegin, startTimeEnd, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 添加或更新招聘直播 🔖
         * @param {number} [id] 
         * @param {string} [title] 
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {Blob} [coverImageFile] 
         * @param {string} [coverImage] 
         * @param {string} [briefIntroduction] 
         * @param {string} [enterprises] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRecruitmentLiveSubmitPostForm(id?: number, title?: string, startTime?: Date, endTime?: Date, coverImageFile?: Blob, coverImage?: string, briefIntroduction?: string, enterprises?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return RecruitmentLiveApiFp(configuration).apiRecruitmentLiveSubmitPostForm(id, title, startTime, endTime, coverImageFile, coverImage, briefIntroduction, enterprises, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * RecruitmentLiveApi - object-oriented interface
 * @export
 * @class RecruitmentLiveApi
 * @extends {BaseAPI}
 */
export class RecruitmentLiveApi extends BaseAPI {
    /**
     * 
     * @summary 删除招聘直播 🔖
     * @param {number} id 直播ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RecruitmentLiveApi
     */
    public async apiRecruitmentLiveDeleteIdDelete(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return RecruitmentLiveApiFp(this.configuration).apiRecruitmentLiveDeleteIdDelete(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取招聘直播详情 🔖
     * @param {number} id 直播ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RecruitmentLiveApi
     */
    public async apiRecruitmentLiveDetailIdGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultRecruitmentLiveOutput>> {
        return RecruitmentLiveApiFp(this.configuration).apiRecruitmentLiveDetailIdGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取招聘直播分页列表 🔖
     * @param {string} [title] ����ؼ���
     * @param {Date} [startTimeBegin] ��ʼʱ�䷶Χ���
     * @param {Date} [startTimeEnd] ��ʼʱ�䷶Χ�յ�
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RecruitmentLiveApi
     */
    public async apiRecruitmentLivePageGet(title?: string, startTimeBegin?: Date, startTimeEnd?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListRecruitmentLiveOutput>> {
        return RecruitmentLiveApiFp(this.configuration).apiRecruitmentLivePageGet(title, startTimeBegin, startTimeEnd, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 添加或更新招聘直播 🔖
     * @param {number} [id] 
     * @param {string} [title] 
     * @param {Date} [startTime] 
     * @param {Date} [endTime] 
     * @param {Blob} [coverImageFile] 
     * @param {string} [coverImage] 
     * @param {string} [briefIntroduction] 
     * @param {string} [enterprises] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RecruitmentLiveApi
     */
    public async apiRecruitmentLiveSubmitPostForm(id?: number, title?: string, startTime?: Date, endTime?: Date, coverImageFile?: Blob, coverImage?: string, briefIntroduction?: string, enterprises?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return RecruitmentLiveApiFp(this.configuration).apiRecruitmentLiveSubmitPostForm(id, title, startTime, endTime, coverImageFile, coverImage, briefIntroduction, enterprises, options).then((request) => request(this.axios, this.basePath));
    }
}
