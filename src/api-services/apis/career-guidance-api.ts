/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultCareerGuidanceOutput } from '../models';
import { AdminResultInt64 } from '../models';
import { AdminResultListObject } from '../models';
import { AdminResultSqlSugarPagedListCareerGuidanceOutput } from '../models';
import { CareerGuidanceVideoEditingInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
/**
 * CareerGuidanceApi - axios parameter creator
 * @export
 */
export const CareerGuidanceApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 批量删除
         * @param {Array<number>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCareerGuidanceBatchDeletePost: async (body?: Array<number>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/careerGuidance/batchDelete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除
         * @param {number} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCareerGuidanceDeletePost: async (body?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/careerGuidance/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取详情
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCareerGuidanceDetailGet: async (id?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/careerGuidance/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询列表
         * @param {number} [type] 类型（1：就业指导 2：求职指导 3：职场能力）
         * @param {string} [title] 就业指导标题
         * @param {string} [lecturer] 讲师
         * @param {boolean} [isPublished] 是否发布线上
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCareerGuidancePageGet: async (type?: number, title?: string, lecturer?: string, isPublished?: boolean, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/careerGuidance/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (type !== undefined) {
                localVarQueryParameter['Type'] = type;
            }

            if (title !== undefined) {
                localVarQueryParameter['Title'] = title;
            }

            if (lecturer !== undefined) {
                localVarQueryParameter['Lecturer'] = lecturer;
            }

            if (isPublished !== undefined) {
                localVarQueryParameter['IsPublished'] = isPublished;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 新增或编辑
         * @param {number} [id] 
         * @param {number} [type] 
         * @param {string} [title] 
         * @param {string} [classHours] 
         * @param {string} [lecturer] 
         * @param {string} [image] 
         * @param {Blob} [imageFile] 
         * @param {Array<CareerGuidanceVideoEditingInput>} [videos] 
         * @param {string} [shortCourseIntroduction] 
         * @param {string} [courseIntroduction] 
         * @param {boolean} [isPublished] 
         * @param {Date} [publishTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCareerGuidanceSavePostForm: async (id?: number, type?: number, title?: string, classHours?: string, lecturer?: string, image?: string, imageFile?: Blob, videos?: Array<CareerGuidanceVideoEditingInput>, shortCourseIntroduction?: string, courseIntroduction?: string, isPublished?: boolean, publishTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/careerGuidance/save`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new FormData();

            // authentication Bearer required


            if (id !== undefined) { 
                localVarFormParams.append('Id', id as any);
            }

            if (type !== undefined) { 
                localVarFormParams.append('Type', type as any);
            }

            if (title !== undefined) { 
                localVarFormParams.append('Title', title as any);
            }

            if (classHours !== undefined) { 
                localVarFormParams.append('ClassHours', classHours as any);
            }

            if (lecturer !== undefined) { 
                localVarFormParams.append('Lecturer', lecturer as any);
            }

            if (image !== undefined) { 
                localVarFormParams.append('Image', image as any);
            }

            if (imageFile !== undefined) { 
                localVarFormParams.append('ImageFile', imageFile as any);
            }
            if (videos) {
                videos.forEach((element) => {
                    localVarFormParams.append('Videos', element as any);
                })
            }

            if (shortCourseIntroduction !== undefined) { 
                localVarFormParams.append('ShortCourseIntroduction', shortCourseIntroduction as any);
            }

            if (courseIntroduction !== undefined) { 
                localVarFormParams.append('CourseIntroduction', courseIntroduction as any);
            }

            if (isPublished !== undefined) { 
                localVarFormParams.append('IsPublished', isPublished as any);
            }

            if (publishTime !== undefined) { 
                localVarFormParams.append('PublishTime', publishTime as any);
            }

            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取职业指导类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCareerGuidanceTypesGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/careerGuidance/types`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CareerGuidanceApi - functional programming interface
 * @export
 */
export const CareerGuidanceApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 批量删除
         * @param {Array<number>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceBatchDeletePost(body?: Array<number>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CareerGuidanceApiAxiosParamCreator(configuration).apiCareerGuidanceBatchDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除
         * @param {number} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceDeletePost(body?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await CareerGuidanceApiAxiosParamCreator(configuration).apiCareerGuidanceDeletePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取详情
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceDetailGet(id?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultCareerGuidanceOutput>>> {
            const localVarAxiosArgs = await CareerGuidanceApiAxiosParamCreator(configuration).apiCareerGuidanceDetailGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询列表
         * @param {number} [type] 类型（1：就业指导 2：求职指导 3：职场能力）
         * @param {string} [title] 就业指导标题
         * @param {string} [lecturer] 讲师
         * @param {boolean} [isPublished] 是否发布线上
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidancePageGet(type?: number, title?: string, lecturer?: string, isPublished?: boolean, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListCareerGuidanceOutput>>> {
            const localVarAxiosArgs = await CareerGuidanceApiAxiosParamCreator(configuration).apiCareerGuidancePageGet(type, title, lecturer, isPublished, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 新增或编辑
         * @param {number} [id] 
         * @param {number} [type] 
         * @param {string} [title] 
         * @param {string} [classHours] 
         * @param {string} [lecturer] 
         * @param {string} [image] 
         * @param {Blob} [imageFile] 
         * @param {Array<CareerGuidanceVideoEditingInput>} [videos] 
         * @param {string} [shortCourseIntroduction] 
         * @param {string} [courseIntroduction] 
         * @param {boolean} [isPublished] 
         * @param {Date} [publishTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceSavePostForm(id?: number, type?: number, title?: string, classHours?: string, lecturer?: string, image?: string, imageFile?: Blob, videos?: Array<CareerGuidanceVideoEditingInput>, shortCourseIntroduction?: string, courseIntroduction?: string, isPublished?: boolean, publishTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await CareerGuidanceApiAxiosParamCreator(configuration).apiCareerGuidanceSavePostForm(id, type, title, classHours, lecturer, image, imageFile, videos, shortCourseIntroduction, courseIntroduction, isPublished, publishTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取职业指导类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceTypesGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListObject>>> {
            const localVarAxiosArgs = await CareerGuidanceApiAxiosParamCreator(configuration).apiCareerGuidanceTypesGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * CareerGuidanceApi - factory interface
 * @export
 */
export const CareerGuidanceApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 批量删除
         * @param {Array<number>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceBatchDeletePost(body?: Array<number>, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CareerGuidanceApiFp(configuration).apiCareerGuidanceBatchDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除
         * @param {number} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceDeletePost(body?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return CareerGuidanceApiFp(configuration).apiCareerGuidanceDeletePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取详情
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceDetailGet(id?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultCareerGuidanceOutput>> {
            return CareerGuidanceApiFp(configuration).apiCareerGuidanceDetailGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询列表
         * @param {number} [type] 类型（1：就业指导 2：求职指导 3：职场能力）
         * @param {string} [title] 就业指导标题
         * @param {string} [lecturer] 讲师
         * @param {boolean} [isPublished] 是否发布线上
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidancePageGet(type?: number, title?: string, lecturer?: string, isPublished?: boolean, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListCareerGuidanceOutput>> {
            return CareerGuidanceApiFp(configuration).apiCareerGuidancePageGet(type, title, lecturer, isPublished, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 新增或编辑
         * @param {number} [id] 
         * @param {number} [type] 
         * @param {string} [title] 
         * @param {string} [classHours] 
         * @param {string} [lecturer] 
         * @param {string} [image] 
         * @param {Blob} [imageFile] 
         * @param {Array<CareerGuidanceVideoEditingInput>} [videos] 
         * @param {string} [shortCourseIntroduction] 
         * @param {string} [courseIntroduction] 
         * @param {boolean} [isPublished] 
         * @param {Date} [publishTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceSavePostForm(id?: number, type?: number, title?: string, classHours?: string, lecturer?: string, image?: string, imageFile?: Blob, videos?: Array<CareerGuidanceVideoEditingInput>, shortCourseIntroduction?: string, courseIntroduction?: string, isPublished?: boolean, publishTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return CareerGuidanceApiFp(configuration).apiCareerGuidanceSavePostForm(id, type, title, classHours, lecturer, image, imageFile, videos, shortCourseIntroduction, courseIntroduction, isPublished, publishTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取职业指导类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCareerGuidanceTypesGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListObject>> {
            return CareerGuidanceApiFp(configuration).apiCareerGuidanceTypesGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CareerGuidanceApi - object-oriented interface
 * @export
 * @class CareerGuidanceApi
 * @extends {BaseAPI}
 */
export class CareerGuidanceApi extends BaseAPI {
    /**
     * 
     * @summary 批量删除
     * @param {Array<number>} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareerGuidanceApi
     */
    public async apiCareerGuidanceBatchDeletePost(body?: Array<number>, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CareerGuidanceApiFp(this.configuration).apiCareerGuidanceBatchDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除
     * @param {number} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareerGuidanceApi
     */
    public async apiCareerGuidanceDeletePost(body?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return CareerGuidanceApiFp(this.configuration).apiCareerGuidanceDeletePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取详情
     * @param {number} [id] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareerGuidanceApi
     */
    public async apiCareerGuidanceDetailGet(id?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultCareerGuidanceOutput>> {
        return CareerGuidanceApiFp(this.configuration).apiCareerGuidanceDetailGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询列表
     * @param {number} [type] 类型（1：就业指导 2：求职指导 3：职场能力）
     * @param {string} [title] 就业指导标题
     * @param {string} [lecturer] 讲师
     * @param {boolean} [isPublished] 是否发布线上
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareerGuidanceApi
     */
    public async apiCareerGuidancePageGet(type?: number, title?: string, lecturer?: string, isPublished?: boolean, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListCareerGuidanceOutput>> {
        return CareerGuidanceApiFp(this.configuration).apiCareerGuidancePageGet(type, title, lecturer, isPublished, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 新增或编辑
     * @param {number} [id] 
     * @param {number} [type] 
     * @param {string} [title] 
     * @param {string} [classHours] 
     * @param {string} [lecturer] 
     * @param {string} [image] 
     * @param {Blob} [imageFile] 
     * @param {Array<CareerGuidanceVideoEditingInput>} [videos] 
     * @param {string} [shortCourseIntroduction] 
     * @param {string} [courseIntroduction] 
     * @param {boolean} [isPublished] 
     * @param {Date} [publishTime] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareerGuidanceApi
     */
    public async apiCareerGuidanceSavePostForm(id?: number, type?: number, title?: string, classHours?: string, lecturer?: string, image?: string, imageFile?: Blob, videos?: Array<CareerGuidanceVideoEditingInput>, shortCourseIntroduction?: string, courseIntroduction?: string, isPublished?: boolean, publishTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return CareerGuidanceApiFp(this.configuration).apiCareerGuidanceSavePostForm(id, type, title, classHours, lecturer, image, imageFile, videos, shortCourseIntroduction, courseIntroduction, isPublished, publishTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取职业指导类型列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareerGuidanceApi
     */
    public async apiCareerGuidanceTypesGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListObject>> {
        return CareerGuidanceApiFp(this.configuration).apiCareerGuidanceTypesGet(options).then((request) => request(this.axios, this.basePath));
    }
}
