/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultListCodeGenConfig } from '../models';
import { AdminResultSysCodeGenConfig } from '../models';
import { CodeGenConfig } from '../models';
/**
 * SysCodeGenConfigApi - axios parameter creator
 * @export
 */
export const SysCodeGenConfigApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取代码生成配置详情 🔖
         * @param {number} [id] 主键Id
         * @param {number} [codeGenId] 代码生成主表ID
         * @param {string} [columnName] 数据库字段名
         * @param {string} [columnKey] 主外键
         * @param {string} [propertyName] 实体属性名
         * @param {number} [columnLength] 字段数据长度
         * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
         * @param {string} [columnComment] 字段描述
         * @param {string} [netType] .NET类型
         * @param {string} [dataType] 数据库中类型（物理类型）
         * @param {string} [nullableNetType] 可空.NET类型
         * @param {string} [effectType] 作用类型（字典）
         * @param {string} [fkConfigId] 外键库标识
         * @param {string} [fkEntityName] 外键实体名称
         * @param {string} [fkTableName] 外键表名称
         * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
         * @param {string} [fkLinkColumnName] 外键链接字段
         * @param {string} [fkDisplayColumns] 外键显示字段
         * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
         * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
         * @param {string} [fkColumnNetType] 外键显示字段.NET类型
         * @param {string} [pidColumn] 父级字段
         * @param {string} [dictTypeCode] 字典code
         * @param {string} [queryType] 查询方式
         * @param {string} [whetherQuery] 是否是查询条件
         * @param {string} [whetherRetract] 列表是否缩进（字典）
         * @param {string} [whetherRequired] 是否必填（字典）
         * @param {string} [whetherSortable] 是否可排序（字典）
         * @param {string} [whetherTable] 列表显示
         * @param {string} [whetherAddUpdate] 增改
         * @param {string} [whetherImport] 导入
         * @param {string} [whetherCommon] 是否是通用字段
         * @param {number} [orderNo] 排序
         * @param {boolean} [isSelectorEffectType] 是否是选择器控件
         * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [extendedPropertyName] 扩展属性名称
         * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysCodeGenConfigDetailGet: async (id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysCodeGenConfig/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            if (codeGenId !== undefined) {
                localVarQueryParameter['CodeGenId'] = codeGenId;
            }

            if (columnName !== undefined) {
                localVarQueryParameter['ColumnName'] = columnName;
            }

            if (columnKey !== undefined) {
                localVarQueryParameter['ColumnKey'] = columnKey;
            }

            if (propertyName !== undefined) {
                localVarQueryParameter['PropertyName'] = propertyName;
            }

            if (columnLength !== undefined) {
                localVarQueryParameter['ColumnLength'] = columnLength;
            }

            if (lowerPropertyName !== undefined) {
                localVarQueryParameter['LowerPropertyName'] = lowerPropertyName;
            }

            if (columnComment !== undefined) {
                localVarQueryParameter['ColumnComment'] = columnComment;
            }

            if (netType !== undefined) {
                localVarQueryParameter['NetType'] = netType;
            }

            if (dataType !== undefined) {
                localVarQueryParameter['DataType'] = dataType;
            }

            if (nullableNetType !== undefined) {
                localVarQueryParameter['NullableNetType'] = nullableNetType;
            }

            if (effectType !== undefined) {
                localVarQueryParameter['EffectType'] = effectType;
            }

            if (fkConfigId !== undefined) {
                localVarQueryParameter['FkConfigId'] = fkConfigId;
            }

            if (fkEntityName !== undefined) {
                localVarQueryParameter['FkEntityName'] = fkEntityName;
            }

            if (fkTableName !== undefined) {
                localVarQueryParameter['FkTableName'] = fkTableName;
            }

            if (lowerFkEntityName !== undefined) {
                localVarQueryParameter['LowerFkEntityName'] = lowerFkEntityName;
            }

            if (fkLinkColumnName !== undefined) {
                localVarQueryParameter['FkLinkColumnName'] = fkLinkColumnName;
            }

            if (fkDisplayColumns !== undefined) {
                localVarQueryParameter['FkDisplayColumns'] = fkDisplayColumns;
            }

            if (fkDisplayColumnList) {
                localVarQueryParameter['FkDisplayColumnList'] = fkDisplayColumnList;
            }

            if (lowerFkDisplayColumnsList) {
                localVarQueryParameter['LowerFkDisplayColumnsList'] = lowerFkDisplayColumnsList;
            }

            if (fkColumnNetType !== undefined) {
                localVarQueryParameter['FkColumnNetType'] = fkColumnNetType;
            }

            if (pidColumn !== undefined) {
                localVarQueryParameter['PidColumn'] = pidColumn;
            }

            if (dictTypeCode !== undefined) {
                localVarQueryParameter['DictTypeCode'] = dictTypeCode;
            }

            if (queryType !== undefined) {
                localVarQueryParameter['QueryType'] = queryType;
            }

            if (whetherQuery !== undefined) {
                localVarQueryParameter['WhetherQuery'] = whetherQuery;
            }

            if (whetherRetract !== undefined) {
                localVarQueryParameter['WhetherRetract'] = whetherRetract;
            }

            if (whetherRequired !== undefined) {
                localVarQueryParameter['WhetherRequired'] = whetherRequired;
            }

            if (whetherSortable !== undefined) {
                localVarQueryParameter['WhetherSortable'] = whetherSortable;
            }

            if (whetherTable !== undefined) {
                localVarQueryParameter['WhetherTable'] = whetherTable;
            }

            if (whetherAddUpdate !== undefined) {
                localVarQueryParameter['WhetherAddUpdate'] = whetherAddUpdate;
            }

            if (whetherImport !== undefined) {
                localVarQueryParameter['WhetherImport'] = whetherImport;
            }

            if (whetherCommon !== undefined) {
                localVarQueryParameter['WhetherCommon'] = whetherCommon;
            }

            if (orderNo !== undefined) {
                localVarQueryParameter['OrderNo'] = orderNo;
            }

            if (isSelectorEffectType !== undefined) {
                localVarQueryParameter['IsSelectorEffectType'] = isSelectorEffectType;
            }

            if (propertyNameTrimEndId !== undefined) {
                localVarQueryParameter['PropertyNameTrimEndId'] = propertyNameTrimEndId;
            }

            if (lowerPropertyNameTrimEndId !== undefined) {
                localVarQueryParameter['LowerPropertyNameTrimEndId'] = lowerPropertyNameTrimEndId;
            }

            if (extendedPropertyName !== undefined) {
                localVarQueryParameter['ExtendedPropertyName'] = extendedPropertyName;
            }

            if (lowerExtendedPropertyName !== undefined) {
                localVarQueryParameter['LowerExtendedPropertyName'] = lowerExtendedPropertyName;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取代码生成配置列表 🔖
         * @param {number} [id] 主键Id
         * @param {number} [codeGenId] 代码生成主表ID
         * @param {string} [columnName] 数据库字段名
         * @param {string} [columnKey] 主外键
         * @param {string} [propertyName] 实体属性名
         * @param {number} [columnLength] 字段数据长度
         * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
         * @param {string} [columnComment] 字段描述
         * @param {string} [netType] .NET类型
         * @param {string} [dataType] 数据库中类型（物理类型）
         * @param {string} [nullableNetType] 可空.NET类型
         * @param {string} [effectType] 作用类型（字典）
         * @param {string} [fkConfigId] 外键库标识
         * @param {string} [fkEntityName] 外键实体名称
         * @param {string} [fkTableName] 外键表名称
         * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
         * @param {string} [fkLinkColumnName] 外键链接字段
         * @param {string} [fkDisplayColumns] 外键显示字段
         * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
         * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
         * @param {string} [fkColumnNetType] 外键显示字段.NET类型
         * @param {string} [pidColumn] 父级字段
         * @param {string} [dictTypeCode] 字典code
         * @param {string} [queryType] 查询方式
         * @param {string} [whetherQuery] 是否是查询条件
         * @param {string} [whetherRetract] 列表是否缩进（字典）
         * @param {string} [whetherRequired] 是否必填（字典）
         * @param {string} [whetherSortable] 是否可排序（字典）
         * @param {string} [whetherTable] 列表显示
         * @param {string} [whetherAddUpdate] 增改
         * @param {string} [whetherImport] 导入
         * @param {string} [whetherCommon] 是否是通用字段
         * @param {number} [orderNo] 排序
         * @param {boolean} [isSelectorEffectType] 是否是选择器控件
         * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [extendedPropertyName] 扩展属性名称
         * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysCodeGenConfigListGet: async (id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysCodeGenConfig/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['Id'] = id;
            }

            if (codeGenId !== undefined) {
                localVarQueryParameter['CodeGenId'] = codeGenId;
            }

            if (columnName !== undefined) {
                localVarQueryParameter['ColumnName'] = columnName;
            }

            if (columnKey !== undefined) {
                localVarQueryParameter['ColumnKey'] = columnKey;
            }

            if (propertyName !== undefined) {
                localVarQueryParameter['PropertyName'] = propertyName;
            }

            if (columnLength !== undefined) {
                localVarQueryParameter['ColumnLength'] = columnLength;
            }

            if (lowerPropertyName !== undefined) {
                localVarQueryParameter['LowerPropertyName'] = lowerPropertyName;
            }

            if (columnComment !== undefined) {
                localVarQueryParameter['ColumnComment'] = columnComment;
            }

            if (netType !== undefined) {
                localVarQueryParameter['NetType'] = netType;
            }

            if (dataType !== undefined) {
                localVarQueryParameter['DataType'] = dataType;
            }

            if (nullableNetType !== undefined) {
                localVarQueryParameter['NullableNetType'] = nullableNetType;
            }

            if (effectType !== undefined) {
                localVarQueryParameter['EffectType'] = effectType;
            }

            if (fkConfigId !== undefined) {
                localVarQueryParameter['FkConfigId'] = fkConfigId;
            }

            if (fkEntityName !== undefined) {
                localVarQueryParameter['FkEntityName'] = fkEntityName;
            }

            if (fkTableName !== undefined) {
                localVarQueryParameter['FkTableName'] = fkTableName;
            }

            if (lowerFkEntityName !== undefined) {
                localVarQueryParameter['LowerFkEntityName'] = lowerFkEntityName;
            }

            if (fkLinkColumnName !== undefined) {
                localVarQueryParameter['FkLinkColumnName'] = fkLinkColumnName;
            }

            if (fkDisplayColumns !== undefined) {
                localVarQueryParameter['FkDisplayColumns'] = fkDisplayColumns;
            }

            if (fkDisplayColumnList) {
                localVarQueryParameter['FkDisplayColumnList'] = fkDisplayColumnList;
            }

            if (lowerFkDisplayColumnsList) {
                localVarQueryParameter['LowerFkDisplayColumnsList'] = lowerFkDisplayColumnsList;
            }

            if (fkColumnNetType !== undefined) {
                localVarQueryParameter['FkColumnNetType'] = fkColumnNetType;
            }

            if (pidColumn !== undefined) {
                localVarQueryParameter['PidColumn'] = pidColumn;
            }

            if (dictTypeCode !== undefined) {
                localVarQueryParameter['DictTypeCode'] = dictTypeCode;
            }

            if (queryType !== undefined) {
                localVarQueryParameter['QueryType'] = queryType;
            }

            if (whetherQuery !== undefined) {
                localVarQueryParameter['WhetherQuery'] = whetherQuery;
            }

            if (whetherRetract !== undefined) {
                localVarQueryParameter['WhetherRetract'] = whetherRetract;
            }

            if (whetherRequired !== undefined) {
                localVarQueryParameter['WhetherRequired'] = whetherRequired;
            }

            if (whetherSortable !== undefined) {
                localVarQueryParameter['WhetherSortable'] = whetherSortable;
            }

            if (whetherTable !== undefined) {
                localVarQueryParameter['WhetherTable'] = whetherTable;
            }

            if (whetherAddUpdate !== undefined) {
                localVarQueryParameter['WhetherAddUpdate'] = whetherAddUpdate;
            }

            if (whetherImport !== undefined) {
                localVarQueryParameter['WhetherImport'] = whetherImport;
            }

            if (whetherCommon !== undefined) {
                localVarQueryParameter['WhetherCommon'] = whetherCommon;
            }

            if (orderNo !== undefined) {
                localVarQueryParameter['OrderNo'] = orderNo;
            }

            if (isSelectorEffectType !== undefined) {
                localVarQueryParameter['IsSelectorEffectType'] = isSelectorEffectType;
            }

            if (propertyNameTrimEndId !== undefined) {
                localVarQueryParameter['PropertyNameTrimEndId'] = propertyNameTrimEndId;
            }

            if (lowerPropertyNameTrimEndId !== undefined) {
                localVarQueryParameter['LowerPropertyNameTrimEndId'] = lowerPropertyNameTrimEndId;
            }

            if (extendedPropertyName !== undefined) {
                localVarQueryParameter['ExtendedPropertyName'] = extendedPropertyName;
            }

            if (lowerExtendedPropertyName !== undefined) {
                localVarQueryParameter['LowerExtendedPropertyName'] = lowerExtendedPropertyName;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新代码生成配置 🔖
         * @param {Array<CodeGenConfig>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysCodeGenConfigUpdatePost: async (body?: Array<CodeGenConfig>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysCodeGenConfig/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysCodeGenConfigApi - functional programming interface
 * @export
 */
export const SysCodeGenConfigApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取代码生成配置详情 🔖
         * @param {number} [id] 主键Id
         * @param {number} [codeGenId] 代码生成主表ID
         * @param {string} [columnName] 数据库字段名
         * @param {string} [columnKey] 主外键
         * @param {string} [propertyName] 实体属性名
         * @param {number} [columnLength] 字段数据长度
         * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
         * @param {string} [columnComment] 字段描述
         * @param {string} [netType] .NET类型
         * @param {string} [dataType] 数据库中类型（物理类型）
         * @param {string} [nullableNetType] 可空.NET类型
         * @param {string} [effectType] 作用类型（字典）
         * @param {string} [fkConfigId] 外键库标识
         * @param {string} [fkEntityName] 外键实体名称
         * @param {string} [fkTableName] 外键表名称
         * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
         * @param {string} [fkLinkColumnName] 外键链接字段
         * @param {string} [fkDisplayColumns] 外键显示字段
         * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
         * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
         * @param {string} [fkColumnNetType] 外键显示字段.NET类型
         * @param {string} [pidColumn] 父级字段
         * @param {string} [dictTypeCode] 字典code
         * @param {string} [queryType] 查询方式
         * @param {string} [whetherQuery] 是否是查询条件
         * @param {string} [whetherRetract] 列表是否缩进（字典）
         * @param {string} [whetherRequired] 是否必填（字典）
         * @param {string} [whetherSortable] 是否可排序（字典）
         * @param {string} [whetherTable] 列表显示
         * @param {string} [whetherAddUpdate] 增改
         * @param {string} [whetherImport] 导入
         * @param {string} [whetherCommon] 是否是通用字段
         * @param {number} [orderNo] 排序
         * @param {boolean} [isSelectorEffectType] 是否是选择器控件
         * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [extendedPropertyName] 扩展属性名称
         * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysCodeGenConfigDetailGet(id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSysCodeGenConfig>>> {
            const localVarAxiosArgs = await SysCodeGenConfigApiAxiosParamCreator(configuration).apiSysCodeGenConfigDetailGet(id, codeGenId, columnName, columnKey, propertyName, columnLength, lowerPropertyName, columnComment, netType, dataType, nullableNetType, effectType, fkConfigId, fkEntityName, fkTableName, lowerFkEntityName, fkLinkColumnName, fkDisplayColumns, fkDisplayColumnList, lowerFkDisplayColumnsList, fkColumnNetType, pidColumn, dictTypeCode, queryType, whetherQuery, whetherRetract, whetherRequired, whetherSortable, whetherTable, whetherAddUpdate, whetherImport, whetherCommon, orderNo, isSelectorEffectType, propertyNameTrimEndId, lowerPropertyNameTrimEndId, extendedPropertyName, lowerExtendedPropertyName, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取代码生成配置列表 🔖
         * @param {number} [id] 主键Id
         * @param {number} [codeGenId] 代码生成主表ID
         * @param {string} [columnName] 数据库字段名
         * @param {string} [columnKey] 主外键
         * @param {string} [propertyName] 实体属性名
         * @param {number} [columnLength] 字段数据长度
         * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
         * @param {string} [columnComment] 字段描述
         * @param {string} [netType] .NET类型
         * @param {string} [dataType] 数据库中类型（物理类型）
         * @param {string} [nullableNetType] 可空.NET类型
         * @param {string} [effectType] 作用类型（字典）
         * @param {string} [fkConfigId] 外键库标识
         * @param {string} [fkEntityName] 外键实体名称
         * @param {string} [fkTableName] 外键表名称
         * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
         * @param {string} [fkLinkColumnName] 外键链接字段
         * @param {string} [fkDisplayColumns] 外键显示字段
         * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
         * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
         * @param {string} [fkColumnNetType] 外键显示字段.NET类型
         * @param {string} [pidColumn] 父级字段
         * @param {string} [dictTypeCode] 字典code
         * @param {string} [queryType] 查询方式
         * @param {string} [whetherQuery] 是否是查询条件
         * @param {string} [whetherRetract] 列表是否缩进（字典）
         * @param {string} [whetherRequired] 是否必填（字典）
         * @param {string} [whetherSortable] 是否可排序（字典）
         * @param {string} [whetherTable] 列表显示
         * @param {string} [whetherAddUpdate] 增改
         * @param {string} [whetherImport] 导入
         * @param {string} [whetherCommon] 是否是通用字段
         * @param {number} [orderNo] 排序
         * @param {boolean} [isSelectorEffectType] 是否是选择器控件
         * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [extendedPropertyName] 扩展属性名称
         * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysCodeGenConfigListGet(id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListCodeGenConfig>>> {
            const localVarAxiosArgs = await SysCodeGenConfigApiAxiosParamCreator(configuration).apiSysCodeGenConfigListGet(id, codeGenId, columnName, columnKey, propertyName, columnLength, lowerPropertyName, columnComment, netType, dataType, nullableNetType, effectType, fkConfigId, fkEntityName, fkTableName, lowerFkEntityName, fkLinkColumnName, fkDisplayColumns, fkDisplayColumnList, lowerFkDisplayColumnsList, fkColumnNetType, pidColumn, dictTypeCode, queryType, whetherQuery, whetherRetract, whetherRequired, whetherSortable, whetherTable, whetherAddUpdate, whetherImport, whetherCommon, orderNo, isSelectorEffectType, propertyNameTrimEndId, lowerPropertyNameTrimEndId, extendedPropertyName, lowerExtendedPropertyName, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新代码生成配置 🔖
         * @param {Array<CodeGenConfig>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysCodeGenConfigUpdatePost(body?: Array<CodeGenConfig>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysCodeGenConfigApiAxiosParamCreator(configuration).apiSysCodeGenConfigUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysCodeGenConfigApi - factory interface
 * @export
 */
export const SysCodeGenConfigApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取代码生成配置详情 🔖
         * @param {number} [id] 主键Id
         * @param {number} [codeGenId] 代码生成主表ID
         * @param {string} [columnName] 数据库字段名
         * @param {string} [columnKey] 主外键
         * @param {string} [propertyName] 实体属性名
         * @param {number} [columnLength] 字段数据长度
         * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
         * @param {string} [columnComment] 字段描述
         * @param {string} [netType] .NET类型
         * @param {string} [dataType] 数据库中类型（物理类型）
         * @param {string} [nullableNetType] 可空.NET类型
         * @param {string} [effectType] 作用类型（字典）
         * @param {string} [fkConfigId] 外键库标识
         * @param {string} [fkEntityName] 外键实体名称
         * @param {string} [fkTableName] 外键表名称
         * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
         * @param {string} [fkLinkColumnName] 外键链接字段
         * @param {string} [fkDisplayColumns] 外键显示字段
         * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
         * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
         * @param {string} [fkColumnNetType] 外键显示字段.NET类型
         * @param {string} [pidColumn] 父级字段
         * @param {string} [dictTypeCode] 字典code
         * @param {string} [queryType] 查询方式
         * @param {string} [whetherQuery] 是否是查询条件
         * @param {string} [whetherRetract] 列表是否缩进（字典）
         * @param {string} [whetherRequired] 是否必填（字典）
         * @param {string} [whetherSortable] 是否可排序（字典）
         * @param {string} [whetherTable] 列表显示
         * @param {string} [whetherAddUpdate] 增改
         * @param {string} [whetherImport] 导入
         * @param {string} [whetherCommon] 是否是通用字段
         * @param {number} [orderNo] 排序
         * @param {boolean} [isSelectorEffectType] 是否是选择器控件
         * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [extendedPropertyName] 扩展属性名称
         * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysCodeGenConfigDetailGet(id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSysCodeGenConfig>> {
            return SysCodeGenConfigApiFp(configuration).apiSysCodeGenConfigDetailGet(id, codeGenId, columnName, columnKey, propertyName, columnLength, lowerPropertyName, columnComment, netType, dataType, nullableNetType, effectType, fkConfigId, fkEntityName, fkTableName, lowerFkEntityName, fkLinkColumnName, fkDisplayColumns, fkDisplayColumnList, lowerFkDisplayColumnsList, fkColumnNetType, pidColumn, dictTypeCode, queryType, whetherQuery, whetherRetract, whetherRequired, whetherSortable, whetherTable, whetherAddUpdate, whetherImport, whetherCommon, orderNo, isSelectorEffectType, propertyNameTrimEndId, lowerPropertyNameTrimEndId, extendedPropertyName, lowerExtendedPropertyName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取代码生成配置列表 🔖
         * @param {number} [id] 主键Id
         * @param {number} [codeGenId] 代码生成主表ID
         * @param {string} [columnName] 数据库字段名
         * @param {string} [columnKey] 主外键
         * @param {string} [propertyName] 实体属性名
         * @param {number} [columnLength] 字段数据长度
         * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
         * @param {string} [columnComment] 字段描述
         * @param {string} [netType] .NET类型
         * @param {string} [dataType] 数据库中类型（物理类型）
         * @param {string} [nullableNetType] 可空.NET类型
         * @param {string} [effectType] 作用类型（字典）
         * @param {string} [fkConfigId] 外键库标识
         * @param {string} [fkEntityName] 外键实体名称
         * @param {string} [fkTableName] 外键表名称
         * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
         * @param {string} [fkLinkColumnName] 外键链接字段
         * @param {string} [fkDisplayColumns] 外键显示字段
         * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
         * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
         * @param {string} [fkColumnNetType] 外键显示字段.NET类型
         * @param {string} [pidColumn] 父级字段
         * @param {string} [dictTypeCode] 字典code
         * @param {string} [queryType] 查询方式
         * @param {string} [whetherQuery] 是否是查询条件
         * @param {string} [whetherRetract] 列表是否缩进（字典）
         * @param {string} [whetherRequired] 是否必填（字典）
         * @param {string} [whetherSortable] 是否可排序（字典）
         * @param {string} [whetherTable] 列表显示
         * @param {string} [whetherAddUpdate] 增改
         * @param {string} [whetherImport] 导入
         * @param {string} [whetherCommon] 是否是通用字段
         * @param {number} [orderNo] 排序
         * @param {boolean} [isSelectorEffectType] 是否是选择器控件
         * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
         * @param {string} [extendedPropertyName] 扩展属性名称
         * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysCodeGenConfigListGet(id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListCodeGenConfig>> {
            return SysCodeGenConfigApiFp(configuration).apiSysCodeGenConfigListGet(id, codeGenId, columnName, columnKey, propertyName, columnLength, lowerPropertyName, columnComment, netType, dataType, nullableNetType, effectType, fkConfigId, fkEntityName, fkTableName, lowerFkEntityName, fkLinkColumnName, fkDisplayColumns, fkDisplayColumnList, lowerFkDisplayColumnsList, fkColumnNetType, pidColumn, dictTypeCode, queryType, whetherQuery, whetherRetract, whetherRequired, whetherSortable, whetherTable, whetherAddUpdate, whetherImport, whetherCommon, orderNo, isSelectorEffectType, propertyNameTrimEndId, lowerPropertyNameTrimEndId, extendedPropertyName, lowerExtendedPropertyName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新代码生成配置 🔖
         * @param {Array<CodeGenConfig>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysCodeGenConfigUpdatePost(body?: Array<CodeGenConfig>, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysCodeGenConfigApiFp(configuration).apiSysCodeGenConfigUpdatePost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysCodeGenConfigApi - object-oriented interface
 * @export
 * @class SysCodeGenConfigApi
 * @extends {BaseAPI}
 */
export class SysCodeGenConfigApi extends BaseAPI {
    /**
     * 
     * @summary 获取代码生成配置详情 🔖
     * @param {number} [id] 主键Id
     * @param {number} [codeGenId] 代码生成主表ID
     * @param {string} [columnName] 数据库字段名
     * @param {string} [columnKey] 主外键
     * @param {string} [propertyName] 实体属性名
     * @param {number} [columnLength] 字段数据长度
     * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
     * @param {string} [columnComment] 字段描述
     * @param {string} [netType] .NET类型
     * @param {string} [dataType] 数据库中类型（物理类型）
     * @param {string} [nullableNetType] 可空.NET类型
     * @param {string} [effectType] 作用类型（字典）
     * @param {string} [fkConfigId] 外键库标识
     * @param {string} [fkEntityName] 外键实体名称
     * @param {string} [fkTableName] 外键表名称
     * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
     * @param {string} [fkLinkColumnName] 外键链接字段
     * @param {string} [fkDisplayColumns] 外键显示字段
     * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
     * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
     * @param {string} [fkColumnNetType] 外键显示字段.NET类型
     * @param {string} [pidColumn] 父级字段
     * @param {string} [dictTypeCode] 字典code
     * @param {string} [queryType] 查询方式
     * @param {string} [whetherQuery] 是否是查询条件
     * @param {string} [whetherRetract] 列表是否缩进（字典）
     * @param {string} [whetherRequired] 是否必填（字典）
     * @param {string} [whetherSortable] 是否可排序（字典）
     * @param {string} [whetherTable] 列表显示
     * @param {string} [whetherAddUpdate] 增改
     * @param {string} [whetherImport] 导入
     * @param {string} [whetherCommon] 是否是通用字段
     * @param {number} [orderNo] 排序
     * @param {boolean} [isSelectorEffectType] 是否是选择器控件
     * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
     * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
     * @param {string} [extendedPropertyName] 扩展属性名称
     * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysCodeGenConfigApi
     */
    public async apiSysCodeGenConfigDetailGet(id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSysCodeGenConfig>> {
        return SysCodeGenConfigApiFp(this.configuration).apiSysCodeGenConfigDetailGet(id, codeGenId, columnName, columnKey, propertyName, columnLength, lowerPropertyName, columnComment, netType, dataType, nullableNetType, effectType, fkConfigId, fkEntityName, fkTableName, lowerFkEntityName, fkLinkColumnName, fkDisplayColumns, fkDisplayColumnList, lowerFkDisplayColumnsList, fkColumnNetType, pidColumn, dictTypeCode, queryType, whetherQuery, whetherRetract, whetherRequired, whetherSortable, whetherTable, whetherAddUpdate, whetherImport, whetherCommon, orderNo, isSelectorEffectType, propertyNameTrimEndId, lowerPropertyNameTrimEndId, extendedPropertyName, lowerExtendedPropertyName, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取代码生成配置列表 🔖
     * @param {number} [id] 主键Id
     * @param {number} [codeGenId] 代码生成主表ID
     * @param {string} [columnName] 数据库字段名
     * @param {string} [columnKey] 主外键
     * @param {string} [propertyName] 实体属性名
     * @param {number} [columnLength] 字段数据长度
     * @param {string} [lowerPropertyName] 数据库字段名(首字母小写)
     * @param {string} [columnComment] 字段描述
     * @param {string} [netType] .NET类型
     * @param {string} [dataType] 数据库中类型（物理类型）
     * @param {string} [nullableNetType] 可空.NET类型
     * @param {string} [effectType] 作用类型（字典）
     * @param {string} [fkConfigId] 外键库标识
     * @param {string} [fkEntityName] 外键实体名称
     * @param {string} [fkTableName] 外键表名称
     * @param {string} [lowerFkEntityName] 外键实体名称(首字母小写)
     * @param {string} [fkLinkColumnName] 外键链接字段
     * @param {string} [fkDisplayColumns] 外键显示字段
     * @param {Array<string>} [fkDisplayColumnList] 外键显示字段
     * @param {Array<string>} [lowerFkDisplayColumnsList] 外键显示字段(首字母小写)
     * @param {string} [fkColumnNetType] 外键显示字段.NET类型
     * @param {string} [pidColumn] 父级字段
     * @param {string} [dictTypeCode] 字典code
     * @param {string} [queryType] 查询方式
     * @param {string} [whetherQuery] 是否是查询条件
     * @param {string} [whetherRetract] 列表是否缩进（字典）
     * @param {string} [whetherRequired] 是否必填（字典）
     * @param {string} [whetherSortable] 是否可排序（字典）
     * @param {string} [whetherTable] 列表显示
     * @param {string} [whetherAddUpdate] 增改
     * @param {string} [whetherImport] 导入
     * @param {string} [whetherCommon] 是否是通用字段
     * @param {number} [orderNo] 排序
     * @param {boolean} [isSelectorEffectType] 是否是选择器控件
     * @param {string} [propertyNameTrimEndId] 去掉尾部Id的属性名
     * @param {string} [lowerPropertyNameTrimEndId] 去掉尾部Id的属性名
     * @param {string} [extendedPropertyName] 扩展属性名称
     * @param {string} [lowerExtendedPropertyName] 首字母小写的扩展属性名称
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysCodeGenConfigApi
     */
    public async apiSysCodeGenConfigListGet(id?: number, codeGenId?: number, columnName?: string, columnKey?: string, propertyName?: string, columnLength?: number, lowerPropertyName?: string, columnComment?: string, netType?: string, dataType?: string, nullableNetType?: string, effectType?: string, fkConfigId?: string, fkEntityName?: string, fkTableName?: string, lowerFkEntityName?: string, fkLinkColumnName?: string, fkDisplayColumns?: string, fkDisplayColumnList?: Array<string>, lowerFkDisplayColumnsList?: Array<string>, fkColumnNetType?: string, pidColumn?: string, dictTypeCode?: string, queryType?: string, whetherQuery?: string, whetherRetract?: string, whetherRequired?: string, whetherSortable?: string, whetherTable?: string, whetherAddUpdate?: string, whetherImport?: string, whetherCommon?: string, orderNo?: number, isSelectorEffectType?: boolean, propertyNameTrimEndId?: string, lowerPropertyNameTrimEndId?: string, extendedPropertyName?: string, lowerExtendedPropertyName?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListCodeGenConfig>> {
        return SysCodeGenConfigApiFp(this.configuration).apiSysCodeGenConfigListGet(id, codeGenId, columnName, columnKey, propertyName, columnLength, lowerPropertyName, columnComment, netType, dataType, nullableNetType, effectType, fkConfigId, fkEntityName, fkTableName, lowerFkEntityName, fkLinkColumnName, fkDisplayColumns, fkDisplayColumnList, lowerFkDisplayColumnsList, fkColumnNetType, pidColumn, dictTypeCode, queryType, whetherQuery, whetherRetract, whetherRequired, whetherSortable, whetherTable, whetherAddUpdate, whetherImport, whetherCommon, orderNo, isSelectorEffectType, propertyNameTrimEndId, lowerPropertyNameTrimEndId, extendedPropertyName, lowerExtendedPropertyName, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新代码生成配置 🔖
     * @param {Array<CodeGenConfig>} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysCodeGenConfigApi
     */
    public async apiSysCodeGenConfigUpdatePost(body?: Array<CodeGenConfig>, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysCodeGenConfigApiFp(this.configuration).apiSysCodeGenConfigUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
