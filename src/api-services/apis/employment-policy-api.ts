/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultEmploymentPolicyArticleOutput } from '../models';
import { AdminResultSqlSugarPagedListEmploymentPolicyArticleOutput } from '../models';
import { AdminResultValueTask } from '../models';
import { EmploymentPolicyArticleEditingInput } from '../models';
import { Filter } from '../models';
import { FilterLogicEnum } from '../models';
import { FilterOperatorEnum } from '../models';
/**
 * EmploymentPolicyApi - axios parameter creator
 * @export
 */
export const EmploymentPolicyApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除文章
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentPolicyDeleteIdDelete: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiEmploymentPolicyDeleteIdDelete.');
            }
            const localVarPath = `/api/employmentPolicy/delete/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取文章详情
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentPolicyDetailIdGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiEmploymentPolicyDetailIdGet.');
            }
            const localVarPath = `/api/employmentPolicy/detail/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询就业政策文章
         * @param {string} [title] 标题
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentPolicyPageGet: async (title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/employmentPolicy/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (title !== undefined) {
                localVarQueryParameter['Title'] = title;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (searchFields) {
                localVarQueryParameter['Search.Fields'] = searchFields;
            }

            if (searchKeyword !== undefined) {
                localVarQueryParameter['Search.Keyword'] = searchKeyword;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (filterLogic !== undefined) {
                localVarQueryParameter['Filter.Logic'] = filterLogic;
            }

            if (filterFilters) {
                localVarQueryParameter['Filter.Filters'] = filterFilters;
            }

            if (filterField !== undefined) {
                localVarQueryParameter['Filter.Field'] = filterField;
            }

            if (filterOperator !== undefined) {
                localVarQueryParameter['Filter.Operator'] = filterOperator;
            }

            if (filterValue !== undefined) {
                localVarQueryParameter['Filter.Value'] = filterValue;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 新增或更新文章
         * @param {EmploymentPolicyArticleEditingInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentPolicySubmitPost: async (body?: EmploymentPolicyArticleEditingInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/employmentPolicy/submit`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EmploymentPolicyApi - functional programming interface
 * @export
 */
export const EmploymentPolicyApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除文章
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicyDeleteIdDelete(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EmploymentPolicyApiAxiosParamCreator(configuration).apiEmploymentPolicyDeleteIdDelete(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取文章详情
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicyDetailIdGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEmploymentPolicyArticleOutput>>> {
            const localVarAxiosArgs = await EmploymentPolicyApiAxiosParamCreator(configuration).apiEmploymentPolicyDetailIdGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询就业政策文章
         * @param {string} [title] 标题
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicyPageGet(title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultSqlSugarPagedListEmploymentPolicyArticleOutput>>> {
            const localVarAxiosArgs = await EmploymentPolicyApiAxiosParamCreator(configuration).apiEmploymentPolicyPageGet(title, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 新增或更新文章
         * @param {EmploymentPolicyArticleEditingInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicySubmitPost(body?: EmploymentPolicyArticleEditingInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultValueTask>>> {
            const localVarAxiosArgs = await EmploymentPolicyApiAxiosParamCreator(configuration).apiEmploymentPolicySubmitPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EmploymentPolicyApi - factory interface
 * @export
 */
export const EmploymentPolicyApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 删除文章
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicyDeleteIdDelete(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EmploymentPolicyApiFp(configuration).apiEmploymentPolicyDeleteIdDelete(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取文章详情
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicyDetailIdGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEmploymentPolicyArticleOutput>> {
            return EmploymentPolicyApiFp(configuration).apiEmploymentPolicyDetailIdGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询就业政策文章
         * @param {string} [title] 标题
         * @param {number} [page] 当前页码
         * @param {number} [pageSize] 页码容量
         * @param {string} [field] 排序字段
         * @param {string} [order] 排序方向
         * @param {string} [descStr] 降序排序
         * @param {Array<string>} [searchFields] 字段名称集合
         * @param {string} [searchKeyword] 关键字
         * @param {string} [keyword] 模糊查询关键字
         * @param {FilterLogicEnum} [filterLogic] 过滤条件
         * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
         * @param {string} [filterField] 字段名称
         * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
         * @param {any} [filterValue] 字段值
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicyPageGet(title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultSqlSugarPagedListEmploymentPolicyArticleOutput>> {
            return EmploymentPolicyApiFp(configuration).apiEmploymentPolicyPageGet(title, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 新增或更新文章
         * @param {EmploymentPolicyArticleEditingInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentPolicySubmitPost(body?: EmploymentPolicyArticleEditingInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultValueTask>> {
            return EmploymentPolicyApiFp(configuration).apiEmploymentPolicySubmitPost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EmploymentPolicyApi - object-oriented interface
 * @export
 * @class EmploymentPolicyApi
 * @extends {BaseAPI}
 */
export class EmploymentPolicyApi extends BaseAPI {
    /**
     * 
     * @summary 删除文章
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentPolicyApi
     */
    public async apiEmploymentPolicyDeleteIdDelete(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EmploymentPolicyApiFp(this.configuration).apiEmploymentPolicyDeleteIdDelete(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取文章详情
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentPolicyApi
     */
    public async apiEmploymentPolicyDetailIdGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEmploymentPolicyArticleOutput>> {
        return EmploymentPolicyApiFp(this.configuration).apiEmploymentPolicyDetailIdGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询就业政策文章
     * @param {string} [title] 标题
     * @param {number} [page] 当前页码
     * @param {number} [pageSize] 页码容量
     * @param {string} [field] 排序字段
     * @param {string} [order] 排序方向
     * @param {string} [descStr] 降序排序
     * @param {Array<string>} [searchFields] 字段名称集合
     * @param {string} [searchKeyword] 关键字
     * @param {string} [keyword] 模糊查询关键字
     * @param {FilterLogicEnum} [filterLogic] 过滤条件
     * @param {Array<Filter>} [filterFilters] 筛选过滤条件子项
     * @param {string} [filterField] 字段名称
     * @param {FilterOperatorEnum} [filterOperator] 逻辑运算符
     * @param {any} [filterValue] 字段值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentPolicyApi
     */
    public async apiEmploymentPolicyPageGet(title?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, searchFields?: Array<string>, searchKeyword?: string, keyword?: string, filterLogic?: FilterLogicEnum, filterFilters?: Array<Filter>, filterField?: string, filterOperator?: FilterOperatorEnum, filterValue?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultSqlSugarPagedListEmploymentPolicyArticleOutput>> {
        return EmploymentPolicyApiFp(this.configuration).apiEmploymentPolicyPageGet(title, page, pageSize, field, order, descStr, searchFields, searchKeyword, keyword, filterLogic, filterFilters, filterField, filterOperator, filterValue, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 新增或更新文章
     * @param {EmploymentPolicyArticleEditingInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentPolicyApi
     */
    public async apiEmploymentPolicySubmitPost(body?: EmploymentPolicyArticleEditingInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultValueTask>> {
        return EmploymentPolicyApiFp(this.configuration).apiEmploymentPolicySubmitPost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
