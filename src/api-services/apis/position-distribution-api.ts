/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultPositionDistributionRecord } from '../models';
/**
 * PositionDistributionApi - axios parameter creator
 * @export
 */
export const PositionDistributionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 查询分布数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionDistributionGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/positionDistribution`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PositionDistributionApi - functional programming interface
 * @export
 */
export const PositionDistributionApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 查询分布数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionDistributionGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultPositionDistributionRecord>>> {
            const localVarAxiosArgs = await PositionDistributionApiAxiosParamCreator(configuration).apiPositionDistributionGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * PositionDistributionApi - factory interface
 * @export
 */
export const PositionDistributionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 查询分布数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionDistributionGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultPositionDistributionRecord>> {
            return PositionDistributionApiFp(configuration).apiPositionDistributionGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PositionDistributionApi - object-oriented interface
 * @export
 * @class PositionDistributionApi
 * @extends {BaseAPI}
 */
export class PositionDistributionApi extends BaseAPI {
    /**
     * 
     * @summary 查询分布数据
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionDistributionApi
     */
    public async apiPositionDistributionGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultPositionDistributionRecord>> {
        return PositionDistributionApiFp(this.configuration).apiPositionDistributionGet(options).then((request) => request(this.axios, this.basePath));
    }
}
