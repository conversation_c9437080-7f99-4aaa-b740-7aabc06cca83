<template>
	<div style="position: relative; padding: 10px" ref="paginationElement" class="pagination_7_16" :class="{ pagination_7_16_is_height_table: isHeightTable }">
		<div ref="table_top_ref" :style="{maxHeight:expandHeader ? '1000px':'0'}" class="table_top_slot">
			<slot name="other" :data="other"></slot>

			<Formrow>
				<slot name="form" :data="other"></slot>
			</Formrow>
			<slot name="headerTop"></slot>
			<div class="pagination_header">
				<div class="left_slot">
					<slot name="headerLeft" :data="other"></slot>
				</div>
				<div class="rigth_tool">
					<SvgIcon v-if="showRefresh" name="iconfont icon-shuaxin" :size="22" title="刷新" @click="reset" />

					<el-dropdown v-if="showExport" trigger="click">
						<!-- <SvgIcon name="iconfont icon-yunxiazai_o" :size="22" title="导出" /> -->
						<!-- <p style="font-size: 16px;cursor: pointer;">导出</p> -->
						<el-button>页面数据导出</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="onImportTable">导出本页数据</el-dropdown-item>
								<el-dropdown-item @click="onImportTableAll">导出全部数据</el-dropdown-item>
								<el-dropdown-item @click="onImportTableSpecific" v-if="showSelection">导出选中数据</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
					<el-popover v-if="showTableHeaderSet" placement="bottom-end" trigger="click" transition="el-zoom-in-top" popper-class="table-tool-popper" :width="300" :persistent="false" @show="onSetTable">
						<template #reference>
							<SvgIcon name="iconfont icon-quanjushezhi_o" :size="22" title="设置" />
						</template>
						<template #default>
							<div class="tool-box">
								<el-tooltip content="拖动进行排序" placement="top-start">
									<SvgIcon name="fa fa-question-circle-o" :size="17" class="ml11" color="#909399" />
								</el-tooltip>
								<el-checkbox v-model="tableHeaderCheckListAll" :indeterminate="checkListIndeterminate" class="ml10 mr1" label="列显示" @change="onCheckAllChange" />
								<el-checkbox v-if="showSerialNo" v-model="isSerialNo" class="ml12 mr1" label="序号" />
								<el-checkbox v-if="showSelection" v-model="isSelection" class="ml12 mr1" label="多选" />
							</div>
							<el-scrollbar>
								<div ref="toolSetRef" class="tool-sortable">
									<div class="tool-sortable-item" v-for="v in columns" :key="v.prop" v-show="!v.hideCheck && !v.fixed" :data-key="v.prop">
										<i class="fa fa-arrows-alt handle cursor-pointer"></i>
										<el-checkbox v-model="v.isCheck" size="default" class="ml12 mr8" :label="v.label" @change="onCheckChange" />
									</div>
								</div>
							</el-scrollbar>
						</template>
					</el-popover>
				</div>
			</div>
		</div>

		<div class="expand_header_button" v-if="showExpand" @click="expand">
			<p v-if="!expandHeader">
				展开选项
				<el-icon><ArrowDown /></el-icon>
			</p>
			<p v-else>
				收起选项
				<el-icon><ArrowUp /></el-icon>
			</p>
		</div>

		<!-- <slot name="table" :tableData="dataObj.tableData" :empty="dataObj.empty" ></slot> -->
		<div class="table">
			<el-affix position="top" :offset="90">
				<el-scrollbar ref="topScroll" :always="true" class="top-scroll" @scroll="handleScrollTop">
					<div class="top-scroll-content" :style="tableWidth"></div>
				</el-scrollbar>
			</el-affix>
			<el-table
				ref="TableRef"
				v-bind="$attrs"
				:height="height || null"
				@selection-change="onSelectionChange"
				@select-all="selectAll"
				:default-sort="defaultSort"
				@sort-change="sortChange"
				:row-key="rowKey"
				:data="dataObj.tableData"
				tooltip-effect="dark"
				style="width: 100%"
				:border="border"
				:stripe="stripe"
				:show-header="showHeader"
				:row-class-name="tableRowClassName"
				:summary-method="summaryMethod"
				:show-summary="showSummary"
				:default-expand-all="defaultExpandAll"
			>
				<el-table-column type="selection" :selectable="selectableFun" :reserve-selection="false" :width="30" v-if="showSelection && isSelection" />
				<el-table-column type="index" label="序号" align="center" fixed="left" :width="60" v-if="showSerialNo && isSerialNo" />
				<el-table-column
					v-for="(item, index) in setHeader"
					:sortable="item.sortable ? item.sortable : false"
					:key="index"
					:label="item.label"
					:fixed="item.fixed"
					:header-align="item.headerAlign"
					:align="item.align"
					:prop="item.prop"
					:width="item.width"
					:min-width="item.minWidth"
					:type="item.type ?? 'default'"
				>
					<template v-if="item.slotHeader">
						<slot :name="item.slotHeader"></slot>
					</template>
					<template #default="scope" v-if="item.slot || item.type">
						<slot :name="item.slot ?? item.type" :row="scope.row"></slot>
					</template>
					<template #default="scope" v-else-if="item.machining && item.prop">
						<p>{{ item.machining(scope.row[item.prop], scope.row) }}</p>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<slot name="footer" :tableData="dataObj.tableData"></slot>
		<el-affix position="bottom" v-if="dataObj.total != 0 && showPagination" :offset="offset">
			<div class="pagination" v-if="dataObj.total != 0" :class="{ flexRight: !slots.paginationleft }">
				<el-row :gutter="20" style="width: 100%">
					<el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" class="mb10 mt10 flex_center">
						<div class="paginationleft">
							<div class="check_all" v-if="showSelection">
								<el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
							</div>
							<slot name="paginationleft" :data="{ data: dataObj.tableData, selectlist }" v-if="allFn && dataObj.total != 0"> </slot>
						</div>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12" class="mb10 mt10 flex_center flex_right">
						<el-pagination
							@size-change="sizeChange"
							v-if="dataObj.total != 0"
							@current-change="currentChange"
							:current-page="dataObj.currPage"
							:page-sizes="pageSize"
							:page-size="dataObj.post.pageSize"
							:layout="`${allFn ? 'total, sizes, prev, pager, next, jumper' : 'prev, pager, next, jumper'}`"
							:total="dataObj.total"
						></el-pagination
					></el-col>
				</el-row>
			</div>
		</el-affix>
		<slot name="bottom" :tableData="dataObj.tableData"></slot>
	</div>
</template>
<script lang="ts" setup name="pagination">
import Sortable from 'sortablejs';
import { computed, reactive, toRefs, watch, getCurrentInstance, nextTick, ref, defineComponent, useSlots, defineAsyncComponent, toRaw, onMounted } from 'vue';
import { useThemeConfig } from '/@/stores/themeConfig';
import { storeToRefs } from 'pinia';

import { ElLoading, ElMessage } from 'element-plus';
import { deepClone } from '/@/utils/arrayOperation';
import { exportExcel } from '/@/utils/exportExcel';
import { useRoute, useRouter } from 'vue-router';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

const route = useRoute();

const props = withDefaults(
	defineProps<{
		isHeightTable?: boolean;
		isHeightTableShifting?: number;
		allFn?: boolean;
		option?: object;
		asyncOption?: object;
		offset?: number;
		defaultPage?: number;
		affixTarget?: string;
		ApiFunction: any;
		showSerialNo?: boolean; // 是否显示序号
		showSelection?: boolean; // 是否显示多选
		showRefresh?: boolean; // 是否显示刷新
		showExport?: boolean; // 是否显示打印
		showTableHeaderSet?: boolean; // 是否显示表头筛选
		columns: any[];
		loadingTarget?: string; // 加载loading显示在哪个元素
		border?: boolean; //边框
		stripe?: boolean; //边框
		showHeader?: boolean;
		defaultExpandAll?: boolean; //有可展开表头是否默认展开
		rowKey?: string;
		showPagination?: boolean;
		tableRowClassName?: (data: { row: any; rowIndex: number }) => string;
		defaultSort: {
			prop?: string;
			order?: string;
		};
		selectableFun?: (row: any, index: number) => boolean;
		showSummary?: boolean;
		summaryMethod?: ({ columns, data }: any) => any;
		showExpand?:boolean
	}>(),
	{
		allFn: true,
		option: () => ({}),
		offset: 0,
		defaultPage: 1,
		affixTarget: '',
		showSelection: false,
		showSerialNo: false,
		showRefresh: false,
		showExport: true,
		showTableHeaderSet: false,
		border: true,
		stripe: true,
		showHeader: true,
		defaultExpandAll: true,
		rowKey: 'id',
		showPagination: true,
		loadingTarget: '.el-container .layout-backtop',
		tableRowClassName: () => '',
		defaultSort: () => ({}),
		selectableFun: function () {
			return true;
		},
		isHeightTable: false,
		isHeightTableShifting: 20,
		showSummary: false,
		summaryMethod: () => ({}),
		showExpand:false
	}
);

const paginationElement = ref<HTMLElement | null>(null);
const table_top_ref = ref<HTMLElement | null>(null);

const heightTable = () => {
	const target = paginationElement.value;
	if (target && table_top_ref.value) {
		return target?.clientHeight - table_top_ref.value.clientHeight - props.isHeightTableShifting - (props.showExpand ? 14  : 0);
	}
	return '';
};

const height = computed(() => {
	if (props.isHeightTable) {
		return heightTable();
	} else {
		return '';
	}
});

const topScroll = ref<HTMLElement>();
const tableWidth = ref({
	width: '0',
});
const tableBarDom = ref<any>();
const setTopScroll = () => {
	if (TableRef.value) {
		tableWidth.value.width = TableRef.value.bodyWidth;
		TableRef.value.doLayout();
		tableBarDom.value = TableRef.value.scrollBarRef.wrapRef;
		tableBarDom.value?.addEventListener('scroll', () => {
			let scrollLeft = tableBarDom.value?.scrollLeft;
			topScroll.value?.scrollTo(scrollLeft || 0, 0);
		});
	}
};

const expandHeader = ref(true);
const expand = () => {
	expandHeader.value = !expandHeader.value;
};


const handleScrollTop = ({ scrollLeft, scrollTop }: { scrollLeft: number; scrollTop: number }) => {
	const currentScrollTop = tableBarDom.value.scrollTop;
	tableBarDom.value?.scrollTo(scrollLeft, currentScrollTop);
};

const emits = defineEmits<{
	(e: 'tableDate', value: any): void;
	(e: 'parameterChange', value: typeof dataObj.post): void;
	(e: 'complete', value: any): void;
	(e: 'sortHeader', value: EmptyObjectType[]): void;
	(e: 'update:columns', value: any): void;
	(e: 'selectionChange', value: EmptyObjectType): void;
}>();

const loading = ref(false);
const setHeader = computed(() => {
	return props.columns.filter((v) => v.isCheck);
});
let loadingTarget: any = null;
watch(
	() => loading.value,
	(newValue) => {
		if (newValue) {
			loadingTarget = ElLoading.service({
				lock: true,
				background: 'rgba(255, 255, 255, 0.7)',
				target: props.loadingTarget,
			});
		} else {
			loadingTarget?.close();
		}
	}
);

const columns = computed({
	get() {
		return props.columns;
	},
	set(value) {
		emits('update:columns', value);
	},
});

//拖拽ref
const toolSetRef = ref();
// 拖拽设置
const onSetTable = () => {
	nextTick(() => {
		const sortable = Sortable.create(toolSetRef.value, {
			handle: '.handle',
			dataIdAttr: 'data-key',
			animation: 150,
			onEnd: () => {
				const headerList: EmptyObjectType[] = [];
				sortable.toArray().forEach((val: any) => {
					props.columns.forEach((v) => {
						if (v.prop === val) headerList.push({ ...v });
					});
				});
				columns.value = headerList;
			},
		});
	});
};

const Formrow = defineAsyncComponent(() => import('/@/components/table/formrow.vue'));

const storesThemeConfig = useThemeConfig();

const { themeConfig } = storeToRefs(storesThemeConfig);

const isSelection = ref(true);
isSelection.value = props.showSelection;
const isSerialNo = ref(true);
isSerialNo.value = props.showSerialNo;
const tableHeaderCheckListAll = ref(true);
const checkListIndeterminate = ref(false);

// tool 列显示全选改变时
const onCheckAllChange = <T,>(val: T) => {
	if (val) props.columns.forEach((v) => (v.isCheck = true));
	else props.columns.forEach((v) => (v.isCheck = false));
	checkListIndeterminate.value = false;
};

// tool 列显示当前项改变时
const onCheckChange = () => {
	const headers = props.columns.filter((v) => v.isCheck).length;
	tableHeaderCheckListAll.value = headers === props.columns.length;
	checkListIndeterminate.value = headers > 0 && headers < props.columns.length;
};

const slots = useSlots();

const offset = computed(() => props.offset);

const dataObj = reactive({
	isNoData: false,
	currPage: props.defaultPage,
	// 是否没有数据
	empty: false,
	tableData: [],
	post: Object.assign({}, props.option, {
		page: props.defaultPage,
		pageSize: 0,
		field: '',
		order: '',
		rid: '',
	}),
	total: 0,
	pageSizeName: 'pageSize',
});

dataObj.post.field = props.defaultSort?.prop || '';
dataObj.post.order = props.defaultSort?.order || '';

const asyncOption = computed(() => props.asyncOption);

const clonePost = Object.assign({}, dataObj.post);
const cloneAsyncPost = Object.assign({}, props.asyncOption);
const cloneDefaultSort = Object.assign({}, props.defaultSort);

const getOriginalOption = () => {
	return {
		option: Object.assign({}, clonePost),
		asyncOption: Object.assign({}, cloneAsyncPost),
		defaultSort: Object.assign({}, cloneDefaultSort),
	};
};

const pagesize = Number(localStorage.getItem(dataObj.pageSizeName) || themeConfig.value.pageSize[0]);

dataObj.post = Object.assign({}, dataObj.post, {
	pageSize: pagesize,
});

let pageSize = computed(() => themeConfig.value.pageSize);

const TableRef = ref<any>(null);
const checkAll = ref(false);
const isIndeterminate = computed(() => {
	if (selectlist.value.length == 0) {
		return false;
	}
	if (selectlist.value.length != dataObj.tableData.length) {
		return true;
	} else {
		return false;
	}
});
const handleCheckAllChange = (value: boolean) => {
	TableRef.value.toggleAllSelection();
};

const selectAll = (value: any[]) => {
	if (value.length > 0) {
		checkAll.value = true;
	} else {
		checkAll.value = false;
	}
};

const other = ref<any>(null);
let getData = (post: any) => {
	dataObj.isNoData = false;
	loading.value = true;
	props
		.ApiFunction(post)
		.then(({ data }: any) => {
			let { result } = data;
			other.value = result;
			let { total, items } = result;
			emits('tableDate', data);
			// console.log(total, items);
			emits('parameterChange', dataObj.post);
			dataObj.total = Number(total || 0);
			dataObj.tableData = items;

			const newData = Object.assign({}, data || {}, {
				total: dataObj.total,
			});
			setTimeout(() => {
				setTopScroll();
			}, 50);
			emits('complete', newData);
			dataObj.empty = dataObj.post.page == 1 && dataObj.total == 0;
			loading.value = false;
		})
		.catch((e) => {
			console.log(e);

			dataObj.total = 0;
			dataObj.tableData = [];

			const newDate = Object.assign({}, post || {}, {
				total: dataObj.total,
			});
			emits('complete', newDate);
			// this.$emit('complete', newDate)
			dataObj.empty = dataObj.post.page == 1 && dataObj.total == 0;
			loading.value = false;
		});
};

watch(
	() => dataObj.post,
	(newValue, oldValue) => {
		const options = Object.assign({}, dataObj.post);
		// 如果page相等说明 变动的是其他数据 page 需重置
		if (newValue && oldValue && oldValue.page === newValue.page && newValue.page !== 1) {
			// dataObj.post.page = 1
			// dataObj.currPage = 1
			getData(options);
		} else {
			getData(options);
		}
	},
	{
		immediate: true,
		deep: true,
	}
);
watch(
	() => props.option,
	(value) => {
		dataObj.post = Object.assign({}, dataObj.post, value, {
			page: 1,
		});
		dataObj.currPage = props.defaultPage;
	},
	{
		deep: true,
	}
);
const sizeChange = (value: any) => {
	localStorage.setItem(dataObj.pageSizeName, value);
	dataObj.post = Object.assign({}, dataObj.post, {
		pageSize: value,
	});
};
const reset = () => {
	dataObj.post.rid = Math.random() + '';
};
const retrunItem = (key: string, keyValue: unknown) => {
	let row = dataObj.tableData.find((item) => item[key] == keyValue);

	return row;
};
const currentadd = (data: Record<string, any>) => {
	if (dataObj.total / 15 > dataObj.currPage) {
		dataObj.currPage++;
		dataObj.post = Object.assign({}, dataObj.post, data, {
			page: dataObj.currPage,
		});
		dataObj.isNoData = false;
	} else {
		dataObj.isNoData = true;
		return;
	}
};

// 表格多选改变时
const selectlist = ref<EmptyObjectType[]>([]);
const onSelectionChange = (val: EmptyObjectType[]) => {
	if (val.length <= 0) {
		checkAll.value = false;
	}
	if (val.length == dataObj.tableData.length) {
		checkAll.value = true;
	}
	selectlist.value = val;
	emits('selectionChange', selectlist.value);
};

// 列排序
const sortChange = (column: any) => {
	dataObj.post.field = column.prop;
	dataObj.post.order = column.order;
	if (!column.order) {
		delete dataObj.post.field;
		delete dataObj.post.order;
	}
};

const currentChange = (value: any) => {
	dataObj.currPage = value;
	dataObj.post = Object.assign({}, dataObj.post, {
		page: value,
	});
};

const asyncOptionReset = () => {
	dataObj.post = Object.assign({}, dataObj.post, asyncOption.value);
};

const onImportTable = () => {
	if (setHeader.value.length <= 0) return ElMessage.error('没有勾选要导出的列');
	exportData(dataObj.tableData);
};

const onImportTableSpecific = () => {
	if (setHeader.value.length <= 0) return ElMessage.error('没有勾选要导出的列');
	if (selectlist.value.length <= 0) return ElMessage.error('没有勾选要导出的行');
	exportData(selectlist.value);
};

const onImportTableAll = () => {
	const param = Object.assign({}, dataObj.post, { page: 1, pageSize: 9999999 });
	loading.value = true;
	props
		.ApiFunction(param)
		.then(({ data }: any) => {
			let { result } = data;
			loading.value = false;
			let { items } = result;
			exportData(items);
		})
		.catch(() => {
			loading.value = false;
		});
};

const exportData = (data: Array<EmptyObjectType>) => {
	if (data.length <= 0) return ElMessage.error('没有数据可以导出');
	let cloneHeader = deepClone(setHeader.value) as any[];

	loading.value = true;
	let exportData = JSON.parse(JSON.stringify(data));

	let header = cloneHeader.filter((item) => {
		if (item.slot == 'action') {
			return false;
		}
		return Boolean(item.prop);
	});

	header.forEach((item) => {
		if (item.machining) {
			exportData.forEach((item2: any) => {
				item2[item.prop] = item.machining(item2[item.prop], item2);
			});
		}
	});

	if (props.showSummary) {
		let countRowList = props.summaryMethod({ columns: header.map((item) => ({ property: item.prop })), data: exportData });
		let countObj: Record<string, any> = {};
		header.forEach((item, index) => {
			console.log(countRowList[index]);
			const stringValue = typeof countRowList[index] == 'string' ? countRowList[index] : countRowList[index]?.children[0];

			countObj[item.prop] = stringValue;
		});
		exportData.push(countObj);
	}

	setTimeout(() => {
		// header.push(p)
		const routeName = route.meta.title;
		console.log(exportData, header);

		exportExcel(exportData, `${routeName}_导出_${exportData.length}条数据_${new Date().toLocaleString()}`, header, '导出数据');
		loading.value = false;
	}, 40);
};

defineExpose({
	asyncOptionReset,
	getOriginalOption,
	reset,
});
</script>
<style lang="scss" scoped>
.pagination_7_16 {
	.expand_header_button {
		height: 16px;
		width: 80px;
		font-size: 12px;
		background: #f0f0f0;
		text-align: center;
		margin: 0 auto;
		line-height: 16px;
		border-radius: 3px;
		overflow: hidden;
		cursor: pointer;
		color: #3b9cb9;
		i {
			font-size: 12px;
		}
	}
	.table_top_slot {
		transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

		overflow: hidden;
		:deep(.el-date-editor) {
			width: 100%;
		}
	}
	.table {
		::v-deep(.el-scrollbar__bar.is-horizontal) {
			height: 12px;
		}
		::v-deep(.el-scrollbar__thumb) {
			background-color: #242424;
		}
	}
	.table:hover {
		.top-scroll {
			visibility: visible;
		}
	}
	.top-scroll {
		width: 100%;
		visibility: hidden;
		.top-scroll-content {
			height: 14px;
		}
	}
}
.pagination_7_16_is_height_table {
	flex: 1;
	height: 0;
}
.pagination {
	.flex_center {
		display: flex;
		align-items: center;
	}
	// display: flex;
	// justify-content: space-between;
	width: 100%;
	// box-sizing: border-box;
	// text-align: right;
	min-height: 56px;
	// line-height: 36px;
	background: #fff;
	// padding: 0 10px;
	.flex_right {
		justify-content: flex-end;
	}
	.paginationleft {
		display: flex;
		width: 100%;
		flex: 1;
		.check_all {
			padding-right: 10px;
			margin-left: 8px;
			display: flex;
			align-items: center;
		}
	}
}

.pagination_header {
	padding: 10px 0;
	display: flex;
	width: 100%;

	.left_slot {
		display: flex;
		flex: 1;
	}

	.rigth_tool {
		display: flex;
		align-items: center;
		i {
			margin:0 5px;
			cursor: pointer;
		}
	}
}

:deep(.el-affix--fixed) {
	z-index: 99 !important;
}

.flexRight {
	align-items: center;
	justify-content: flex-end !important;
}

:deep(.el-pager li.active) {
	background: #fff;
	color: #374dc3;
	border: 1px solid #374dc3;
	opacity: 1;
	border-radius: 3px;
}

:deep(.el-pager li) {
	background: #fff;
	margin: 0 4px;
	padding: 0;
	font-size: 12px;
	width: 24px;
	height: 24px;
	line-height: 24px;
	min-width: 24px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
	color: #374dc3;
}

:deep(.el-pagination button) {
	background: #fff !important;
}

:deep(.el-pagination) {
	margin: 0 !important;
}
</style>
