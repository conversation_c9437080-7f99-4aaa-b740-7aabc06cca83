<template>
	<div class="editor-container">
		<Toolbar :editor="editorRef" :mode="mode" />
		<Editor :mode="mode" :defaultConfig="state.editorConfig" :style="{ height }" v-model="state.editorVal" @onCreated="handleCreated" @onChange="handleChange" />
	</div>
</template>

<script setup lang="ts" name="wngEditor">
// https://www.wangeditor.com/v5/for-frame.html#vue3
import '@wangeditor/editor/dist/css/style.css';
import { reactive, shallowRef, watch, onBeforeUnmount } from 'vue';
import { IDomEditor } from '@wangeditor/editor';
import { Toolbar, Editor } from '@wangeditor/editor-for-vue';
import { ElMessage } from 'element-plus';
// 移除服务器上传相关的导入
// import { getAPI } from '/@/utils/axios-utils';
// import { SysFileApi } from '/@/api-services/api';

// 定义父组件传过来的值
const props = defineProps({
	// 是否禁用
	disable: {
		type: Boolean,
		default: () => false,
	},
	// 内容框默认 placeholder
	placeholder: {
		type: String,
		default: () => '请输入内容...',
	},
	// https://www.wangeditor.com/v5/getting-started.html#mode-%E6%A8%A1%E5%BC%8F
	// 模式，可选 <default|simple>，默认 default
	mode: {
		type: String,
		default: () => 'default',
	},
	// 高度
	height: {
		type: String,
		default: () => '310px',
	},
	// 双向绑定，用于获取 editor.getHtml()
	getHtml: String,
	// 双向绑定，用于获取 editor.getText()
	getText: String,
});

// 定义子组件向父组件传值/事件
const emit = defineEmits(['update:getHtml', 'update:getText']);

// 定义变量内容
const editorRef = shallowRef();
const state = reactive({
	editorConfig: {
		placeholder: props.placeholder,
		// 菜单配置
		MENU_CONF: {
			uploadImage: {
				fieldName: 'file',
				customUpload(file: File, insertFn: any) {
					// 检查文件类型
					if (!file.type.startsWith('image/')) {
						ElMessage.error('请选择图片文件！');
						return;
					}

					// 检查文件大小（限制为10MB）
					const maxSize = 10 * 1024 * 1024; // 10MB
					if (file.size > maxSize) {
						ElMessage.error('图片文件大小不能超过10MB！');
						return;
					}

					// 显示上传进度提示
					const loadingMessage = ElMessage({
						message: '正在处理图片...',
						type: 'info',
						duration: 0, // 不自动关闭
					});

					// 图片压缩和转换函数
					const processImage = (file: File): Promise<string> => {
						return new Promise((resolve, reject) => {
							const canvas = document.createElement('canvas');
							const ctx = canvas.getContext('2d');
							const img = new Image();

							img.onload = () => {
								try {
									// 计算压缩后的尺寸（最大宽度1920px）
									const maxWidth = 1920;
									const maxHeight = 1080;
									let { width, height } = img;

									if (width > maxWidth || height > maxHeight) {
										const ratio = Math.min(maxWidth / width, maxHeight / height);
										width = Math.floor(width * ratio);
										height = Math.floor(height * ratio);
									}

									// 设置canvas尺寸
									canvas.width = width;
									canvas.height = height;

									// 绘制压缩后的图片
									ctx?.drawImage(img, 0, 0, width, height);

									// 转换为base64，根据文件大小选择质量
									const quality = file.size > 1024 * 1024 ? 0.7 : 0.9; // 大于1MB使用0.7质量
									const base64Url = canvas.toDataURL('image/jpeg', quality);

									resolve(base64Url);
								} catch (error) {
									reject(error);
								}
							};

							img.onerror = () => reject(new Error('图片加载失败'));

							// 读取文件
							const reader = new FileReader();
							reader.onload = (e) => {
								img.src = e.target?.result as string;
							};
							reader.onerror = () => reject(new Error('文件读取失败'));
							reader.readAsDataURL(file);
						});
					};

					// 处理图片
					processImage(file)
						.then((base64Url) => {
							loadingMessage.close();
							// 使用insertFn插入base64格式的图片
							insertFn(base64Url, file.name, base64Url);
							ElMessage.success('图片插入成功！');
						})
						.catch((error) => {
							loadingMessage.close();
							console.error('图片处理失败:', error);
							ElMessage.error('图片处理失败：' + error.message);
						});
				},
			},
			insertImage: {
				checkImage(src: string, alt: string, href: string): boolean | string | undefined {
					// 支持base64格式和http/https格式的图片
					if (!src) {
						return '图片地址不能为空';
					}

					// 检查是否为base64格式或http/https格式
					if (src.startsWith('data:image/') || src.indexOf('http') === 0) {
						return true;
					}

					return '图片格式不正确，请使用有效的图片地址或上传图片文件';
				},
			},
		},
	},
	editorVal: props.getHtml,
});

// 编辑器回调函数
const handleCreated = (editor: IDomEditor) => {
	editorRef.value = editor;
};
// 编辑器内容改变时
const handleChange = (editor: IDomEditor) => {
	emit('update:getHtml', editor.getHtml());
	emit('update:getText', editor.getText());
};
// 页面销毁时
onBeforeUnmount(() => {
	const editor = editorRef.value;
	if (editor == null) return;
	editor.destroy();
});
// 监听是否禁用改变
// https://gitee.com/lyt-top/vue-next-admin/issues/I4LM7I
watch(
	() => props.disable,
	(bool) => {
		const editor = editorRef.value;
		if (editor == null) return;
		bool ? editor.disable() : editor.enable();
	},
	{
		deep: true,
	}
);
// 监听双向绑定值改变，用于回显
watch(
	() => props.getHtml,
	(val) => {
		state.editorVal = val;
	},
	{
		deep: true,
	}
);

// 暴露 editorRef
defineExpose({
	ref: editorRef,
});
</script>
<style lang="less">
.editor-container {
	overflow-y: hidden;
	.w-e-bar-item {
		.w-e-select-list {
			height: 150px;
			z-index: 10 !important;
		}
	}
	.w-e-text-container {
		// 文本框里面的层级调低
		//z-index: 3 !important;
	}
	.w-e-toolbar {
		// 给工具栏换行
		flex-wrap: wrap;
		z-index: 4 !important;
	}
	.w-e-menu {
		// 最重要的一句代码
		z-index: auto !important;
		.w-e-droplist {
			// 触发工具栏后的显示框调高
			z-index: 2 !important;
		}
	}
}
</style>
