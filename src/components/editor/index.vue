<template>
	<div class="editor-container">
		<Toolbar :editor="editorRef" :mode="mode" :key="`toolbar-${editor<PERSON>ey}`" />
		<Editor
			:mode="mode"
			:defaultConfig="state.editorConfig"
			:style="{ height }"
			v-model="state.editorVal"
			@onCreated="handleCreated"
			@onChange="handleChange"
			:key="`editor-${editorKey}`"
		/>
	</div>
</template>

<script setup lang="ts" name="wngEditor">
// https://www.wangeditor.com/v5/for-frame.html#vue3
import '@wangeditor/editor/dist/css/style.css';
import { reactive, ref, shallowRef, watch, onBeforeUnmount, markRaw, nextTick } from 'vue';
import { IDomEditor } from '@wangeditor/editor';
import { Toolbar, Editor } from '@wangeditor/editor-for-vue';
import { ElMessage } from 'element-plus';
// 移除服务器上传相关的导入
// import { getAPI } from '/@/utils/axios-utils';
// import { SysFileApi } from '/@/api-services/api';

// 定义父组件传过来的值
const props = defineProps({
	// 是否禁用
	disable: {
		type: Boolean,
		default: () => false,
	},
	// 内容框默认 placeholder
	placeholder: {
		type: String,
		default: () => '请输入内容...',
	},
	// https://www.wangeditor.com/v5/getting-started.html#mode-%E6%A8%A1%E5%BC%8F
	// 模式，可选 <default|simple>，默认 default
	mode: {
		type: String,
		default: () => 'default',
	},
	// 高度
	height: {
		type: String,
		default: () => '310px',
	},
	// 双向绑定，用于获取 editor.getHtml()
	getHtml: String,
	// 双向绑定，用于获取 editor.getText()
	getText: String,
});

// 定义子组件向父组件传值/事件
const emit = defineEmits(['update:getHtml', 'update:getText']);

// 定义变量内容
const editorRef = shallowRef<IDomEditor>();
const editorKey = ref(Date.now()); // 用于强制重新渲染的key
const state = reactive({
	editorConfig: {
		placeholder: props.placeholder,
		// 菜单配置
		MENU_CONF: {
			uploadImage: {
				fieldName: 'file',
				customUpload(file: File, insertFn: any) {
					// 检查文件类型
					if (!file.type.startsWith('image/')) {
						ElMessage.error('请选择图片文件！');
						return;
					}

					// 检查文件大小（限制为5MB，避免base64过大）
					const maxSize = 5 * 1024 * 1024; // 5MB
					if (file.size > maxSize) {
						ElMessage.error('图片文件大小不能超过5MB！');
						return;
					}

					// 使用FileReader将图片转换为base64
					const reader = new FileReader();
					reader.onload = (e) => {
						try {
							const base64Url = e.target?.result as string;
							if (base64Url && base64Url.startsWith('data:image/')) {
								// 验证base64数据的完整性
								const base64Data = base64Url.split(',')[1];
								if (!base64Data || base64Data.length < 100) {
									ElMessage.error('图片数据不完整！');
									return;
								}

								// 确保编辑器已经初始化
								if (!editorRef.value) {
									ElMessage.error('编辑器未初始化！');
									return;
								}

								// 使用insertFn插入base64格式的图片
								// 参数：url, alt, href
								insertFn(base64Url, file.name || '图片', '');
							} else {
								ElMessage.error('图片格式转换失败！');
							}
						} catch (error) {
							console.error('图片处理失败:', error);
							ElMessage.error('图片处理失败！');
						}
					};

					reader.onerror = () => {
						ElMessage.error('图片读取失败！');
					};

					// 开始读取文件为base64格式
					reader.readAsDataURL(file);
				},
			},
			insertImage: {
				checkImage(src: string, alt: string, href: string): boolean | string | undefined {
					// 支持base64格式和http/https格式的图片
					if (!src) {
						return '图片地址不能为空';
					}

					// 检查是否为base64格式或http/https格式
					if (src.startsWith('data:image/') || src.indexOf('http') === 0) {
						return true;
					}

					return '图片格式不正确，请使用有效的图片地址或上传图片文件';
				},
			},
		},
	},
	editorVal: props.getHtml,
});

// 编辑器回调函数
const handleCreated = (editor: IDomEditor) => {
	// 使用nextTick确保DOM已更新
	nextTick(() => {
		editorRef.value = editor;
		console.log('编辑器初始化完成');
	});
};

// 编辑器内容改变时
const handleChange = (editor: IDomEditor) => {
	try {
		if (editor && typeof editor.getHtml === 'function' && typeof editor.getText === 'function') {
			emit('update:getHtml', editor.getHtml());
			emit('update:getText', editor.getText());
		}
	} catch (error) {
		console.error('编辑器内容更新失败:', error);
	}
};

// 页面销毁时
onBeforeUnmount(() => {
	try {
		const editor = editorRef.value;
		if (editor && typeof editor.destroy === 'function') {
			editor.destroy();
		}
		editorRef.value = undefined;
	} catch (error) {
		console.error('编辑器销毁失败:', error);
	}
});
// 监听是否禁用改变
// https://gitee.com/lyt-top/vue-next-admin/issues/I4LM7I
watch(
	() => props.disable,
	(bool) => {
		const editor = editorRef.value;
		if (editor == null) return;
		bool ? editor.disable() : editor.enable();
	},
	{
		deep: true,
	}
);
// 监听双向绑定值改变，用于回显
watch(
	() => props.getHtml,
	(val) => {
		try {
			// 使用nextTick确保在下一个tick中更新，避免响应式冲突
			nextTick(() => {
				if (val !== state.editorVal) {
					state.editorVal = val;
				}
			});
		} catch (error) {
			console.error('编辑器内容设置失败:', error);
		}
	},
	{
		deep: false, // 改为false，避免深度监听大数据
		immediate: false,
	}
);

// 重置编辑器的方法
const resetEditor = () => {
	try {
		// 销毁当前编辑器
		const editor = editorRef.value;
		if (editor && typeof editor.destroy === 'function') {
			editor.destroy();
		}
		editorRef.value = undefined;

		// 更新key以强制重新渲染
		editorKey.value = Date.now();

		console.log('编辑器已重置');
	} catch (error) {
		console.error('编辑器重置失败:', error);
	}
};

// 暴露 editorRef 和重置方法
defineExpose({
	ref: editorRef,
	resetEditor,
});
</script>
<style lang="less">
.editor-container {
	overflow-y: hidden;
	.w-e-bar-item {
		.w-e-select-list {
			height: 150px;
			z-index: 10 !important;
		}
	}
	.w-e-text-container {
		// 文本框里面的层级调低
		//z-index: 3 !important;
	}
	.w-e-toolbar {
		// 给工具栏换行
		flex-wrap: wrap;
		z-index: 4 !important;
	}
	.w-e-menu {
		// 最重要的一句代码
		z-index: auto !important;
		.w-e-droplist {
			// 触发工具栏后的显示框调高
			z-index: 2 !important;
		}
	}
}
</style>
