/**
 * 判断两数组字符串是否相同（用于按钮权限验证），数组字符串中存在相同时会自动去重（按钮权限标识不会重复）
 * @param news 新数据
 * @param old 源数据
 * @returns 两数组相同返回 `true`，反之则反
 */
export function judgementSameArr(newArr: unknown[] | string[], oldArr: string[]): boolean {
	const news = removeDuplicate(newArr);
	const olds = removeDuplicate(oldArr);
	let count = 0;
	const leng = news.length;
	for (let i in olds) {
		for (let j in news) {
			if (olds[i] === news[j]) count++;
		}
	}
	return count === leng ? true : false;
}

/**
 * 判断两个对象是否相同
 * @param a 要比较的对象一
 * @param b 要比较的对象二
 * @returns 相同返回 true，反之则反
 */
export function isObjectValueEqual<T>(a: T, b: T): boolean {
	if (!a || !b) return false;
	let aProps = Object.getOwnPropertyNames(a);
	let bProps = Object.getOwnPropertyNames(b);
	if (aProps.length != bProps.length) return false;
	for (let i = 0; i < aProps.length; i++) {
		let propName = aProps[i];
		let propA = a[propName];
		let propB = b[propName];
		if (!b.hasOwnProperty(propName)) return false;
		if (propA instanceof Object) {
			if (!isObjectValueEqual(propA, propB)) return false;
		} else if (propA !== propB) {
			return false;
		}
	}
	return true;
}

/**
 * 数组、数组对象去重
 * @param arr 数组内容
 * @param attr 需要去重的键值（数组对象）
 * @returns
 */
export function removeDuplicate(arr: EmptyArrayType, attr?: string) {
	if (!Object.keys(arr).length) {
		return arr;
	} else {
		if (attr) {
			const obj: EmptyObjectType = {};
			return arr.reduce((cur: EmptyArrayType[], item: EmptyArrayType) => {
				obj[item[attr]] ? '' : (obj[item[attr]] = true && item[attr] && cur.push(item));
				return cur;
			}, []);
		} else {
			return [...new Set(arr)];
		}
	}
}

/* 数组、对象深拷贝
 * @param value 需要拷贝内容
 * @returns
 */
export const clone = <T>(value: T): T => {
	if (!value) return value;

	// 数组
	if (Array.isArray(value)) return value.map((item) => clone(item)) as unknown as T;

	// 普通对象
	if (typeof value === 'object') {
		return Object.fromEntries(
			Object.entries(value).map(([k, v]: [string, any]) => {
				return [k, clone(v)];
			})
		) as unknown as T;
	}
	// 基本类型
	return value;
};

export const deepClone = (target: [] | object): [] | object => {
	//深拷贝
	// 定义一个变量待会返回
	let result: any;
	// 如果当前需要深拷贝的是一个对象的话
	if (typeof target === 'object') {
	  // 如果是一个数组的话
	  if (Array.isArray(target)) {
		result = []; // 将result赋值为一个数组，并且执行遍历
		for (let i in target) {
		  // 递归克隆数组中的每一项
		  result.push(deepClone(target[i]));
		}
		// 判断如果当前的值是null的话；直接赋值为null
	  } else if (target === null) {
		result = null;
		// 判断如果当前的值是一个RegExp对象的话，直接赋值
	  } else if (target.constructor === RegExp) {
		result = target;
	  } else {
		// 否则是普通对象，直接for in循环，递归赋值对象的所有值
		result = {};
		for (let i in target) {
		  result[i] = deepClone(target[i]);
		}
	  }
	  // 如果不是对象的话，就是基本数据类型，那么直接赋值
	} else {
	  result = target;
	}
	// 返回最终结果
	return result;
  };
