// vite.config.ts
import vue from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/@vitejs+plugin-vue@5.2.4_vite@5.4.19_@types+node@22.15.30_less@4.3.0_sass@1.89.1_terser_e3fa59446404da2d6f2fc3c62f9bd19c/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import { defineConfig, loadEnv } from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.30_less@4.3.0_sass@1.89.1_terser@5.41.0/node_modules/vite/dist/node/index.js";
import vueSetupExtend from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/vite-plugin-vue-setup-extend@0.4.0_vite@5.4.19_@types+node@22.15.30_less@4.3.0_sass@1.89.1_terser@5.41.0_/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
import compression from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/vite-plugin-compression2@1.4.0_rollup@4.42.0_vite@5.4.19_@types+node@22.15.30_less@4.3.0_sass@1.89.1_terser@5.41.0_/node_modules/vite-plugin-compression2/dist/index.mjs";

// src/utils/build.ts
import importToCDN from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/vite-plugin-cdn-import@1.0.1_rollup@4.42.0_vite@5.4.19_@types+node@22.15.30_less@4.3.0_sass@1.89.1_terser@5.41.0_/node_modules/vite-plugin-cdn-import/dist/index.js";
var buildConfig = {
  cdn() {
    return importToCDN({
      prodUrl: "https://unpkg.com/{name}@{version}/{path}",
      modules: [
        // autoComplete('vue'),
        // autoComplete('axios'),
        {
          name: "vue",
          var: "Vue",
          path: "dist/vue.global.js"
        },
        {
          name: "vue-demi",
          var: "VueDemi",
          path: "lib/index.iife.js"
        },
        {
          name: "vue-router",
          var: "VueRouter",
          path: "dist/vue-router.global.js"
        },
        {
          name: "element-plus",
          var: "ElementPlus",
          path: "dist/index.full.js"
        }
        // {
        // 	name: '@element-plus/icons-vue',
        // 	var: 'ElementPlusIconsVue',
        // 	path: 'dist/index.iife.min.js',
        // },
        // {
        // 	name: 'echarts',
        // 	var: 'echarts',
        // 	path: 'dist/echarts.min.js',
        // },
        // {
        // 	name: 'echarts-gl',
        // 	var: 'echarts-gl',
        // 	path: 'dist/echarts-gl.min.js',
        // },
        // {
        // 	name: 'echarts-wordcloud',
        // 	var: 'echarts-wordcloud',
        // 	path: 'dist/echarts-wordcloud.min.js',
        // },
        // {
        // 	name: 'vue-i18n',
        // 	var: 'VueI18n',
        // 	path: 'dist/vue-i18n.global.min.js',
        // },
        // {
        // 	name: 'jsplumb',
        // 	var: 'jsPlumb',
        // 	path: 'dist/js/jsplumb.min.js',
        // },
        // {
        // 	name: 'cropperjs',
        // 	var: 'Cropper',
        // 	path: 'dist/cropper.min.js',
        // },
        // {
        // 	name: 'sortablejs',
        // 	var: 'Sortable',
        // 	path: 'Sortable.min.js',
        // },
        // {
        // 	name: 'qrcodejs2-fixes',
        // 	var: 'QRCode',
        // 	path: 'qrcode.min.js',
        // },
        // {
        // 	name: 'print-js',
        // 	var: 'printJS',
        // 	path: 'dist/print.min.js',
        // },
        // {
        // 	name: '@wangeditor/editor',
        // 	var: 'wangEditor',
        // 	path: 'dist/index.min.js',
        // },
        // {
        // 	name: '@wangeditor/editor-for-vue',
        // 	var: 'WangEditorForVue',
        // 	path: 'dist/index.min.js',
        // },
        // {
        // 	name: 'vue-grid-layout',
        // 	var: 'VueGridLayout',
        // 	path: 'https://cdn.jsdelivr.net/npm/vue-grid-layout@3.0.0-beta1/dist/vue-grid-layout.umd.min.js',
        // },
      ]
    });
  },
  external: [
    "vue",
    // 'axios',
    "vue-router",
    "element-plus"
    // '@element-plus/icons-vue',
    // 'echarts',
    // 'echarts-gl',
    // 'echarts-wordcloud',
    // 'vue-i18n',
    // 'jsplumb',
    // 'cropperjs',
    // 'sortablejs',
    // 'qrcodejs2-fixes',
    // 'print-js',
    // '@wangeditor/editor',
    // '@wangeditor/editor-for-vue',
    // 'vue-grid-layout',
  ]
};

// vite.config.ts
import vueJsx from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.2.0_vite@5.4.19_@types+node@22.15.30_less@4.3.0_sass@1.89.1_te_2f3e2667c5e54f9923ba783a44de660c/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { CodeInspectorPlugin } from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/code-inspector-plugin@0.18.3/node_modules/code-inspector-plugin/dist/index.mjs";
import fs from "fs";
import { visualizer } from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.42.0/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { webUpdateNotice } from "file:///Users/<USER>/code/qianduan/industrial-park-vue/node_modules/.pnpm/@plugin-web-update-notification+vite@1.8.1_vite@5.4.19_@types+node@22.15.30_less@4.3.0_sass@1.89.1_terser@5.41.0_/node_modules/@plugin-web-update-notification/vite/dist/index.js";
var __vite_injected_original_dirname = "/Users/<USER>/code/qianduan/industrial-park-vue";
var pathResolve = (dir) => {
  return resolve(__vite_injected_original_dirname, ".", dir);
};
var alias = {
  "/@": pathResolve("./src/"),
  "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js"
};
var viteConfig = defineConfig((mode) => {
  const env = loadEnv(mode.mode, process.cwd());
  fs.writeFileSync("./public/config.js", `window.__env__ = ${JSON.stringify(env, null, 2)} `);
  return {
    plugins: [
      visualizer({ open: false }),
      // 开启可视化分析页面
      CodeInspectorPlugin({
        bundler: "vite",
        hotKeys: ["shiftKey"]
      }),
      vue(),
      vueJsx(),
      webUpdateNotice({
        versionType: "build_timestamp",
        notificationConfig: {
          placement: "topLeft"
        },
        notificationProps: {
          title: "\u{1F4E2} \u7CFB\u7EDF\u66F4\u65B0",
          description: "\u7CFB\u7EDF\u66F4\u65B0\u5566\uFF0C\u8BF7\u5237\u65B0\u9875\u9762\uFF01",
          buttonText: "\u5237\u65B0",
          dismissButtonText: "\u5FFD\u7565"
        }
      }),
      vueSetupExtend(),
      compression({
        deleteOriginalAssets: false,
        // 是否删除源文件
        threshold: 5120,
        // 对大于 5KB 文件进行 gzip 压缩，单位Bytes
        skipIfLargerOrEqual: true
        // 如果压缩后的文件大小等于或大于原始文件，则跳过压缩
        // algorithm: 'gzip', // 压缩算法，可选[‘gzip’，‘brotliCompress’，‘deflate’，‘deflateRaw’]
        // exclude: [/\.(br)$/, /\.(gz)$/], // 排除指定文件
      }),
      JSON.parse(env.VITE_OPEN_CDN) ? buildConfig.cdn() : null
    ],
    root: process.cwd(),
    resolve: { alias },
    base: mode.command === "serve" ? "./" : env.VITE_PUBLIC_PATH,
    optimizeDeps: { exclude: ["vue-demi"] },
    server: {
      host: "0.0.0.0",
      port: env.VITE_PORT,
      open: JSON.parse(env.VITE_OPEN),
      hmr: true,
      proxy: {
        "^/[Uu]pload": {
          target: env.VITE_API_URL,
          changeOrigin: true
        }
      }
    },
    build: {
      outDir: "dist",
      chunkSizeWarningLimit: 1500,
      assetsInlineLimit: 5e3,
      // 小于此阈值的导入或引用资源将内联为 base64 编码
      sourcemap: false,
      // 构建后是否生成 source map 文件
      extractComments: false,
      // 移除注释
      minify: "terser",
      // 启用后 terserOptions 配置才有效
      terserOptions: {
        compress: {
          drop_console: true,
          // 生产环境时移除console
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          chunkFileNames: "assets/js/[name]-[hash].js",
          // 引入文件名的名称
          entryFileNames: "assets/js/[name]-[hash].js",
          // 包的入口文件名称
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
          // 资源文件像 字体，图片等
          manualChunks(id) {
            if (id.includes("node_modules")) {
              return id.toString().match(/\/node_modules\/(?!.pnpm)(?<moduleName>[^\/]*)\//)?.groups.moduleName ?? "vender";
            }
          }
        },
        ...JSON.parse(env.VITE_OPEN_CDN) ? { external: buildConfig.external } : {}
      }
    },
    css: { preprocessorOptions: { css: { charset: false }, scss: { silenceDeprecations: ["legacy-js-api", "global-builtin", "fs-importer-cwd", "import"] } } },
    define: {
      __VUE_I18N_LEGACY_API__: JSON.stringify(false),
      __VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
      __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
      __NEXT_VERSION__: JSON.stringify(process.env.npm_package_version),
      __NEXT_NAME__: JSON.stringify(process.env.npm_package_name)
    }
  };
});
var vite_config_default = viteConfig;
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
