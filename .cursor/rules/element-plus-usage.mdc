---
description: this rules for element-plus used
globs: 
alwaysApply: false
---
# Element Plus使用规范

## 组件库配置
项目使用Element Plus 2.8.8作为UI组件库，全局引入配置在：[main.ts](mdc:src/main.ts)

## 常用组件使用规范

### 表格组件
```vue
<template>
  <el-table 
    :data="tableData" 
    :loading="loading"
    stripe
    border
    style="width: 100%"
  >
    <el-table-column prop="name" label="姓名" width="120" />
    <el-table-column prop="email" label="邮箱" />
    <el-table-column label="操作" width="200">
      <template #default="{ row }">
        <el-button type="primary" size="small" @click="handleEdit(row)">
          编辑
        </el-button>
        <el-button type="danger" size="small" @click="handleDelete(row)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  
  <!-- 分页组件 -->
  <el-pagination
    v-model:current-page="pagination.current"
    v-model:page-size="pagination.size"
    :total="pagination.total"
    :page-sizes="[10, 20, 50, 100]"
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>
```

### 表单组件
```vue
<template>
  <el-form 
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="100px"
  >
    <el-form-item label="用户名" prop="username">
      <el-input v-model="formData.username" placeholder="请输入用户名" />
    </el-form-item>
    
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="formData.email" type="email" placeholder="请输入邮箱" />
    </el-form-item>
    
    <el-form-item label="角色" prop="role">
      <el-select v-model="formData.role" placeholder="请选择角色">
        <el-option label="管理员" value="admin" />
        <el-option label="用户" value="user" />
      </el-select>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'

const formRef = ref<FormInstance>()
const formData = reactive({
  username: '',
  email: '',
  role: ''
})

const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    // 提交逻辑
  } catch (error) {
    console.log('表单验证失败', error)
  }
}
</script>
```

### 对话框组件
```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
  >
    <!-- 对话框内容 -->
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>
```

### 消息提示使用
```typescript
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

// 成功消息
ElMessage.success('操作成功')

// 错误消息
ElMessage.error('操作失败')

// 确认对话框
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 执行删除操作
    await deleteApi(row.id)
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消操作
  }
}

// 通知
ElNotification({
  title: '系统通知',
  message: '有新的消息',
  type: 'info',
  duration: 3000
})
```

## 图标使用
项目使用Element Plus图标库，参考：[utils/other.ts](mdc:src/utils/other.ts)

```vue
<template>
  <!-- 使用Element Plus图标 -->
  <el-button :icon="Edit" type="primary">编辑</el-button>
  <el-button :icon="Delete" type="danger">删除</el-button>
</template>

<script setup lang="ts">
import { Edit, Delete } from '@element-plus/icons-vue'
</script>
```

## 主题定制
项目主题配置参考：[theme/index.scss](mdc:src/theme/index.scss)
- 支持自定义主题色
- 响应式布局适配
- 暗色模式支持
