---
description: 
globs: 
alwaysApply: false
---
# Cursor Rules 总览

## 规则文件导航

本项目为工业园区Vue3企业级管理系统配置了完整的Cursor Rules，帮助AI更好地理解项目架构和开发规范。

### 📋 规则文件列表

1. **[项目架构规则](mdc:.cursor/rules/project-architecture.mdc)**
   - 技术栈概述
   - 目录结构规范
   - 核心文件说明
   - 架构设计原则

2. **[Vue开发规范](mdc:.cursor/rules/vue-development.mdc)**
   - Composition API规范
   - 组件结构标准
   - 状态管理规范
   - 路由配置规范

3. **[TypeScript开发标准](mdc:.cursor/rules/typescript-standards.mdc)**
   - 类型定义规范
   - 接口设计标准
   - 组件类型规范
   - API类型定义

4. **[Element Plus使用规范](mdc:.cursor/rules/element-plus-usage.mdc)**
   - 组件库配置
   - 常用组件使用
   - 主题定制规范
   - 图标使用指南

5. **[代码质量与最佳实践](mdc:.cursor/rules/code-quality.mdc)**
   - 命名规范
   - 代码组织原则
   - 错误处理规范
   - 性能优化指南

6. **[开发工作流程](mdc:.cursor/rules/development-workflow.mdc)**
   - 项目启动流程
   - 开发环境配置
   - 调试和测试
   - 构建部署流程

## 🚀 快速开始

### 项目核心信息
- **技术栈**: Vue 3.5.13 + TypeScript 5.7.2 + Element Plus 2.8.8 + Vite 5.4.9
- **状态管理**: Pinia 2.2.8
- **路由管理**: Vue Router 4.5.0
- **包管理器**: PNPM
- **代码规范**: ESLint + Prettier

### 关键文件路径
```
├── [src/main.ts](mdc:src/main.ts)                    # 应用入口
├── [src/App.vue](mdc:src/App.vue)                    # 根组件
├── [src/router/index.ts](mdc:src/router/index.ts)    # 路由配置
├── [src/stores/index.ts](mdc:src/stores/index.ts)    # 状态管理
├── [vite.config.ts](mdc:vite.config.ts)              # 构建配置
├── [package.json](mdc:package.json)                  # 项目配置
└── [tsconfig.json](mdc:tsconfig.json)                # TS配置
```

### 开发命令
```bash
pnpm install    # 安装依赖
pnpm run dev    # 启动开发
pnpm run build  # 构建生产
pnpm run lint-fix  # 代码检查
```

## 🎯 AI助手使用指南

当你需要帮助时，AI助手会根据这些规则：

1. **理解项目架构** - 基于分层架构和模块化设计
2. **遵循代码规范** - 使用TypeScript严格模式和ESLint规则
3. **应用最佳实践** - 组件设计、状态管理、错误处理等
4. **保持一致性** - 命名规范、文件组织、代码风格等

## 📝 规则更新

当项目架构或规范发生变化时，请及时更新对应的规则文件，确保AI助手能够提供准确的帮助。

---

*这些规则基于项目当前架构分析生成，旨在提升开发效率和代码质量。*
