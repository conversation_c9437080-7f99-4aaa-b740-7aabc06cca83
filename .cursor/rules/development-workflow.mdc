---
description: 
globs: 
alwaysApply: false
---
# 开发工作流程

## 项目启动流程
参考项目说明：[README.md](mdc:README.md)

### 环境要求
- Node.js 16+ 版本
- PNPM 包管理器
- 现代浏览器支持（Chrome ≥ 87, Firefox ≥ 78, Safari ≥ 13）

### 开发命令
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev

# 构建生产版本
pnpm run build

# 代码格式化
pnpm run format

# 代码检查和修复
pnpm run lint-fix
```

## 开发环境配置

### Vite配置
项目使用Vite作为构建工具，配置文件：[vite.config.ts](mdc:vite.config.ts)
- 支持热更新（HMR）
- 代码分割和压缩
- CDN资源优化
- 环境变量管理

### 环境变量
```bash
# 开发环境 .env.development
VITE_PORT=3000
VITE_OPEN=true
VITE_API_URL=http://localhost:8080
VITE_PUBLIC_PATH=/
VITE_OPEN_CDN=false

# 生产环境 .env.production
VITE_API_URL=https://api.production.com
VITE_PUBLIC_PATH=/admin/
VITE_OPEN_CDN=true
```

## 代码开发流程

### 1. 功能开发步骤
```bash
# 1. 创建功能分支
git checkout -b feature/user-management

# 2. 开发功能
# - 创建API接口（src/api/）
# - 创建页面组件（src/views/）
# - 添加路由配置（src/router/）
# - 更新状态管理（src/stores/）

# 3. 代码检查
pnpm run lint-fix
pnpm run format

# 4. 提交代码
git add .
git commit -m "feat: 添加用户管理功能"

# 5. 推送分支
git push origin feature/user-management
```

### 2. 组件开发模板
```vue
<template>
  <div class="page-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" inline>
        <el-form-item label="关键词">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="action-card">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" :loading="loading">
        <!-- 表格列定义 -->
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  
  .search-card,
  .action-card,
  .table-card {
    margin-bottom: 20px;
  }
}
</style>
```

## 调试和测试

### 开发工具
- Vue DevTools：浏览器扩展，用于调试Vue组件
- Vite DevTools：内置开发工具
- 代码检查器：配置在[vite.config.ts](mdc:vite.config.ts)中

### 调试技巧
```typescript
// 1. 使用console.log调试
console.log('调试信息:', data)

// 2. 使用debugger断点
const handleSubmit = () => {
  debugger // 浏览器会在此处暂停
  // 处理逻辑
}

// 3. 使用Vue DevTools
// 在组件中可以直接查看响应式数据状态
```

## 构建和部署

### 构建配置
参考构建工具：[utils/build.ts](mdc:src/utils/build.ts)
```bash
# 开发构建
pnpm run build

# API构建（如果需要）
pnpm run build-api
```

### 部署检查清单
- [ ] 环境变量配置正确
- [ ] API接口地址更新
- [ ] 静态资源路径配置
- [ ] 浏览器兼容性测试
- [ ] 性能优化检查

## 常见问题解决

### 1. 依赖安装问题
```bash
# 清除缓存重新安装
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

### 2. 热更新失败
```bash
# 重启开发服务器
pnpm run dev
```

### 3. 类型错误
```bash
# 检查TypeScript配置
npx tsc --noEmit
```

### 4. ESLint错误
```bash
# 自动修复代码格式
pnpm run lint-fix
```

## 团队协作规范

### 代码审查要点
- 代码符合项目规范
- 类型定义完整
- 错误处理完善
- 性能考虑合理
- 测试覆盖充分

### 分支管理
- `main`：主分支，稳定版本
- `develop`：开发分支，集成测试
- `feature/*`：功能分支
- `hotfix/*`：紧急修复分支
