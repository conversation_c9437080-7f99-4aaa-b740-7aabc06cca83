---
description: 这是整个项目架构规则
globs: 
alwaysApply: false
---
# 项目架构规则

## 技术栈概述
这是一个基于Admin.NET框架的Vue3企业级管理系统，采用以下核心技术：
- Vue 3.5.13 + Composition API + TypeScript 5.7.2
- Element Plus 2.8.8 UI组件库
- Vite 5.4.9 构建工具
- Pinia 2.2.8 状态管理
- Vue Router 4.5.0 路由管理

## 目录结构规范
```
src/
├── api/           # API接口层 - 按业务模块组织
├── stores/        # 状态管理层 - Pinia stores
├── router/        # 路由配置层 - 支持前后端双模式
├── views/         # 视图组件层 - 页面级组件
├── layout/        # 布局组件层 - 通用布局
├── components/    # 通用组件层 - 可复用组件
├── utils/         # 工具函数层 - 纯函数工具
├── assets/        # 静态资源层
├── types/         # TypeScript类型定义
├── i18n/          # 国际化配置
└── directive/     # Vue指令
```

## 核心文件说明
- 应用入口：[main.ts](mdc:src/main.ts)
- 路由配置：[router/index.ts](mdc:src/router/index.ts)
- 状态管理：[stores/index.ts](mdc:src/stores/index.ts)
- 构建配置：[vite.config.ts](mdc:vite.config.ts)
- 类型配置：[tsconfig.json](mdc:tsconfig.json)
- 包管理：[package.json](mdc:package.json)

## 架构原则
1. **分层解耦**：严格按照分层架构组织代码
2. **模块化**：按业务功能模块化组织
3. **类型安全**：全面使用TypeScript
4. **组件复用**：提取通用组件到components目录
5. **状态集中**：使用Pinia进行状态管理
