---
description: 
globs: 
alwaysApply: true
---
# 代码质量与最佳实践

## 代码规范配置
项目配置了完整的代码质量工具链：
- ESLint配置：[eslint.config.mjs](mdc:eslint.config.mjs)
- Prettier配置：[.prettierrc.cjs](mdc:.prettierrc.cjs)
- TypeScript配置：[tsconfig.json](mdc:tsconfig.json)

## 命名规范

### 文件命名
- **组件文件**：PascalCase，如 `UserManagement.vue`
- **工具文件**：camelCase，如 `commonFunction.ts`
- **类型文件**：camelCase，如 `userTypes.ts`
- **常量文件**：camelCase，如 `apiConstants.ts`

### 变量命名
```typescript
// 常量：UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'
const MAX_RETRY_COUNT = 3

// 变量和函数：camelCase
const userName = 'admin'
const isLoading = ref(false)
const handleSubmit = () => {}

// 类型和接口：PascalCase
interface UserInfo {
  id: number
  name: string
}

type ApiResponse<T> = {
  data: T
  message: string
}
```

## 代码组织原则

### 单一职责原则
每个函数、组件只负责一个功能：
```typescript
// ❌ 不好的做法
const handleUserAction = (action: string, user: User) => {
  if (action === 'create') {
    // 创建用户逻辑
  } else if (action === 'update') {
    // 更新用户逻辑
  } else if (action === 'delete') {
    // 删除用户逻辑
  }
}

// ✅ 好的做法
const createUser = (user: User) => { /* 创建逻辑 */ }
const updateUser = (user: User) => { /* 更新逻辑 */ }
const deleteUser = (userId: number) => { /* 删除逻辑 */ }
```

### 函数纯净性
优先使用纯函数，参考工具函数：[utils/arrayOperation.ts](mdc:src/utils/arrayOperation.ts)
```typescript
// ✅ 纯函数
export const formatDate = (date: Date, format: string): string => {
  // 格式化逻辑，不修改输入参数
  return formattedDate
}

// ✅ 不可变数据操作
export const addItem = <T>(array: T[], item: T): T[] => {
  return [...array, item] // 返回新数组，不修改原数组
}
```

## 错误处理规范

### API错误处理
参考请求工具：[utils/request.ts](mdc:src/utils/request.ts)
```typescript
// 统一错误处理
const handleApiError = (error: any) => {
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response
    ElMessage.error(data.message || '请求失败')
  } else if (error.request) {
    // 网络错误
    ElMessage.error('网络连接失败')
  } else {
    // 其他错误
    ElMessage.error('未知错误')
  }
}

// 组件中的错误处理
const fetchData = async () => {
  try {
    loading.value = true
    const response = await getUserList(queryParams)
    tableData.value = response.data
  } catch (error) {
    handleApiError(error)
  } finally {
    loading.value = false
  }
}
```

### 表单验证错误处理
```typescript
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    // 提交逻辑
  } catch (validationError) {
    // 验证失败，Element Plus会自动显示错误信息
    console.log('表单验证失败:', validationError)
  }
}
```

## 性能优化原则

### 组件懒加载
```typescript
// 路由懒加载
const routes = [
  {
    path: '/user',
    component: () => import('/@/views/system/user/index.vue')
  }
]

// 组件懒加载
const AsyncComponent = defineAsyncComponent(() => 
  import('/@/components/HeavyComponent.vue')
)
```

### 响应式数据优化
```typescript
// ✅ 使用shallowRef处理大型对象
const largeData = shallowRef({})

// ✅ 使用computed缓存计算结果
const expensiveValue = computed(() => {
  return heavyCalculation(props.data)
})

// ✅ 使用watchEffect处理副作用
watchEffect(() => {
  if (props.visible) {
    // 只在visible为true时执行
    fetchData()
  }
})
```

## 代码注释规范
```typescript
/**
 * 用户管理相关API
 * @description 提供用户的增删改查功能
 */

/**
 * 获取用户列表
 * @param params 查询参数
 * @param params.page 页码
 * @param params.size 每页数量
 * @param params.keyword 搜索关键词
 * @returns 用户列表数据
 */
export const getUserList = async (params: QueryParams): Promise<UserListResponse> => {
  // 实现逻辑
}

// 单行注释用于解释复杂逻辑
const processData = (data: any[]) => {
  // 过滤掉无效数据
  return data.filter(item => item.status === 'active')
}
```

## Git提交规范
参考项目配置，使用conventional commits规范：
```bash
# 功能开发
git commit -m "feat: 添加用户管理功能"

# 问题修复
git commit -m "fix: 修复登录状态丢失问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 代码重构
git commit -m "refactor: 重构用户状态管理逻辑"
```
