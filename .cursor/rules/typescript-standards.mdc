---
description: 
globs: 
alwaysApply: false
---
# TypeScript开发标准

## 类型定义规范
项目使用严格的TypeScript配置，参考：[tsconfig.json](mdc:tsconfig.json)

### 接口定义
```typescript
// 用户信息接口
interface UserInfo {
  id: number
  username: string
  email: string
  roles: string[]
  avatar?: string
  createTime: string
}

// API响应接口
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页接口
interface PageResult<T> {
  records: T[]
  total: number
  current: number
  size: number
}
```

### 类型导入导出
```typescript
// 类型定义文件 src/types/user.ts
export interface UserInfo {
  // ...
}

export type UserRole = 'admin' | 'user' | 'guest'

// 组件中使用
import type { UserInfo, UserRole } from '/@/types/user'
```

## 组件Props类型
```typescript
// 定义Props接口
interface TableProps {
  data: any[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
  }
  onPageChange?: (page: number, size: number) => void
}

// 使用withDefaults设置默认值
const props = withDefaults(defineProps<TableProps>(), {
  loading: false,
  pagination: () => ({
    current: 1,
    pageSize: 10,
    total: 0
  })
})
```

## 事件类型定义
```typescript
// 定义Emits类型
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string, oldValue: string]
  'submit': [data: FormData]
}>()
```

## API类型定义
参考API工具：[utils/axios-utils.ts](mdc:src/utils/axios-utils.ts)

```typescript
// 请求参数类型
interface LoginParams {
  username: string
  password: string
  captcha: string
}

// 响应数据类型
interface LoginResponse {
  token: string
  userInfo: UserInfo
  permissions: string[]
}

// API函数类型
export const login = (params: LoginParams): Promise<ApiResponse<LoginResponse>> => {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data: params
  })
}
```

## 工具函数类型
参考工具函数：[utils/commonFunction.ts](mdc:src/utils/commonFunction.ts)

```typescript
// 泛型工具函数
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj))
}

// 类型守卫
export function isString(value: unknown): value is string {
  return typeof value === 'string'
}

// 联合类型
export type Theme = 'light' | 'dark' | 'auto'
export type Language = 'zh-cn' | 'en' | 'zh-tw'
```

## 路径别名配置
项目配置了路径别名 `/@/` 指向 `src/`，在导入时使用：
```typescript
import { request } from '/@/utils/request'
import type { UserInfo } from '/@/types/user'
import UserList from '/@/views/system/user/index.vue'
```
