---
description: 
globs: 
alwaysApply: false
---
# Vue开发规范

## Composition API规范
项目使用Vue 3 Composition API + setup语法糖，遵循以下规范：

### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, reactive, computed, onMounted } from 'vue'
import type { ComponentType } from '/@/types'

// 2. 定义Props和Emits
interface Props {
  title: string
  data?: any[]
}
const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

const emit = defineEmits<{
  change: [value: string]
  update: [data: any]
}>()

// 3. 响应式数据
const loading = ref(false)
const formData = reactive({
  name: '',
  email: ''
})

// 4. 计算属性
const computedValue = computed(() => {
  return props.title.toUpperCase()
})

// 5. 方法定义
const handleSubmit = () => {
  // 处理逻辑
}

// 6. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped lang="scss">
// 样式定义
</style>
```

## 组件命名规范
- **页面组件**：使用PascalCase，如 `UserManagement.vue`
- **通用组件**：使用PascalCase，如 `DataTable.vue`
- **组件文件夹**：使用kebab-case，如 `user-management/`

## 状态管理规范
使用Pinia进行状态管理，参考：[stores/userInfo.ts](mdc:src/stores/userInfo.ts)

```typescript
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo>({})
  
  // 计算属性
  const isLoggedIn = computed(() => !!userInfo.value.token)
  
  // 方法
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
  }
  
  return {
    userInfo,
    isLoggedIn,
    setUserInfo
  }
})
```

## 路由配置规范
参考路由配置：[router/index.ts](mdc:src/router/index.ts)
- 支持前端控制路由和后端控制路由双模式
- 路由懒加载：`component: () => import('/@/views/xxx.vue')`
- 路由元信息配置：权限、缓存、标题等

## API调用规范
参考请求工具：[utils/request.ts](mdc:src/utils/request.ts)
```typescript
// API接口定义
export const getUserList = (params: QueryParams) => {
  return request<UserListResponse>({
    url: '/api/user/list',
    method: 'get',
    params
  })
}

// 组件中使用
const { data, loading, error } = await getUserList(queryParams)
```
